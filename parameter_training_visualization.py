#!/usr/bin/env python3
"""
AI安全防御系统参数训练与可视化模块
实现多目标优化、帕累托前沿分析、参数敏感性分析等高级可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Polygon
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from scipy.optimize import minimize
from scipy.stats import pearsonr
import itertools
import json
import os
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class TrainingResult:
    """训练结果数据结构"""
    params: Dict[str, float]
    f1_score: float  # 错误拒绝风险
    f2_score: float  # 错误接受风险  
    f3_score: float  # 用户体验损失
    overall_score: float
    iteration: int

class ParameterAnalyzer:
    """参数分析和可视化类"""
    
    def __init__(self):
        # 定义所有可训练的超参数及其范围
        self.trainable_params = {
            # 多目标优化权重 (核心参数)
            "w1_dangerous_weight": (0.1, 0.8),      # F1权重：错误拒绝
            "w2_progressive_weight": (0.1, 0.8),    # F2权重：错误接受
            "w3_clarification_weight": (0.1, 0.8),  # F3权重：用户体验
            
            # 马尔可夫状态转移参数
            "gamma": (0.5, 0.95),                   # 当前轮权重γ
            "beta": (0.3, 0.8),                     # 前一轮状态权重β
            
            # 决策阈值参数
            "reject_threshold": (0.6, 0.9),         # 拒绝阈值τ_reject
            "clarify_threshold": (0.3, 0.7),        # 澄清阈值τ_clarify
            
            # 惩罚机制参数
            "delta1_evasion_penalty": (0.1, 0.4),   # δ1：回避惩罚
            "delta2_single_attack_penalty": (0.1, 0.5), # δ2：单轮攻击惩罚
            "delta3_behavioral_drift_penalty": (0.05, 0.3), # δ3：行为漂移惩罚
            
            # 升级检测参数
            "high_escalation_threshold": (0.5, 0.8),     # 高升级阈值
            "moderate_escalation_threshold": (0.3, 0.6), # 中等升级阈值
            
            # 可信度参数
            "low_credibility_threshold": (0.2, 0.4),     # 低可信度阈值
            "high_credibility_threshold": (0.5, 0.8),    # 高可信度阈值
            "credibility_penalty_weight": (0.1, 0.3),    # 可信度惩罚权重
            
            # 轨迹分析参数
            "trajectory_penalty_weight": (0.05, 0.25),   # 轨迹惩罚权重
            "eta_optimization_factor": (0.8, 1.2),       # η：优化因子
            
            # 其他参数
            "fast_track_confidence_threshold": (0.8, 0.95), # 快速通道阈值
            "risk_lock_threshold": (2, 5),               # 风险锁定阈值
        }
        
        # 历史权重参数（特殊处理）
        self.omega_weights_bounds = [(0.1, 0.6), (0.05, 0.3), (0.05, 0.2)]
        
        self.training_history = []
        
    def get_param_info(self) -> Dict:
        """获取参数信息用于LLM查询"""
        param_info = {
            "total_params": len(self.trainable_params) + 1,  # +1 for omega_weights
            "categories": {
                "多目标优化权重": ["w1_dangerous_weight", "w2_progressive_weight", "w3_clarification_weight"],
                "马尔可夫参数": ["gamma", "beta"],
                "决策阈值": ["reject_threshold", "clarify_threshold"],
                "惩罚机制": ["delta1_evasion_penalty", "delta2_single_attack_penalty", "delta3_behavioral_drift_penalty"],
                "升级检测": ["high_escalation_threshold", "moderate_escalation_threshold"],
                "可信度评估": ["low_credibility_threshold", "high_credibility_threshold", "credibility_penalty_weight"],
                "轨迹分析": ["trajectory_penalty_weight", "eta_optimization_factor"],
                "系统配置": ["fast_track_confidence_threshold", "risk_lock_threshold"]
            },
            "key_relationships": {
                "权重约束": "w1 + w2 + w3 应该接近1.0",
                "阈值关系": "reject_threshold > clarify_threshold",
                "可信度关系": "high_credibility_threshold > low_credibility_threshold",
                "升级关系": "high_escalation_threshold > moderate_escalation_threshold"
            }
        }
        return param_info
        
    def generate_random_params(self, n_samples: int = 100) -> List[Dict]:
        """生成随机参数组合用于训练"""
        param_sets = []
        
        for _ in range(n_samples):
            params = {}
            
            # 生成基础参数
            for param_name, (min_val, max_val) in self.trainable_params.items():
                if param_name == "risk_lock_threshold":
                    params[param_name] = int(np.random.uniform(min_val, max_val))
                else:
                    params[param_name] = np.random.uniform(min_val, max_val)
            
            # 生成omega_weights并归一化
            omega_raw = [np.random.uniform(low, high) for low, high in self.omega_weights_bounds]
            omega_sum = sum(omega_raw)
            params["omega_weights"] = [w/omega_sum for w in omega_raw]
            
            # 确保权重约束：w1 + w2 + w3 = 1.0
            w_total = params["w1_dangerous_weight"] + params["w2_progressive_weight"] + params["w3_clarification_weight"]
            params["w1_dangerous_weight"] /= w_total
            params["w2_progressive_weight"] /= w_total
            params["w3_clarification_weight"] /= w_total
            
            # 确保阈值关系
            if params["reject_threshold"] <= params["clarify_threshold"]:
                params["reject_threshold"] = params["clarify_threshold"] + 0.1
                
            if params["high_credibility_threshold"] <= params["low_credibility_threshold"]:
                params["high_credibility_threshold"] = params["low_credibility_threshold"] + 0.1
                
            if params["high_escalation_threshold"] <= params["moderate_escalation_threshold"]:
                params["high_escalation_threshold"] = params["moderate_escalation_threshold"] + 0.1
            
            param_sets.append(params)
            
        return param_sets
    
    def simulate_performance(self, params: Dict) -> Tuple[float, float, float, float]:
        """
        模拟参数性能（实际应用中应该用真实的测试数据）
        返回：F1(错误拒绝), F2(错误接受), F3(用户体验), 总体评分
        """
        # 这里使用简化的性能模拟，实际应该用真实测试集
        w1, w2, w3 = params["w1_dangerous_weight"], params["w2_progressive_weight"], params["w3_clarification_weight"]
        
        # F1: 错误拒绝风险 (权重过高会导致过度拒绝)
        f1 = w1 * 0.3 + (params["reject_threshold"] - 0.5) * 0.4 + np.random.normal(0, 0.05)
        
        # F2: 错误接受风险 (权重过低会导致接受危险内容)
        f2 = (1 - w2) * 0.4 + (1 - params["delta1_evasion_penalty"]) * 0.3 + np.random.normal(0, 0.05)
        
        # F3: 用户体验损失 (过度澄清会影响体验)
        f3 = w3 * 0.5 + (params["clarify_threshold"] - 0.3) * 0.3 + np.random.normal(0, 0.05)
        
        # 总体评分 (帕累托最优化目标)
        overall = w1 * f1 + w2 * f2 + w3 * f3
        
        return max(0, min(1, f1)), max(0, min(1, f2)), max(0, min(1, f3)), max(0, min(1, overall))

    def plot_multi_objective_ternary(self, results: List[TrainingResult], save_path: str = None):
        """
        1. 多目标权重三元图 ⭐⭐⭐⭐⭐
        展示w₁、w₂、w₃权重组合对F₁、F₂、F₃目标的影响
        """
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('权重分布三元图', '帕累托前沿分析'),
            specs=[[{"type": "scatter"}, {"type": "scatter3d"}]]
        )

        # 提取权重和性能数据
        w1_vals = [r.params["w1_dangerous_weight"] for r in results]
        w2_vals = [r.params["w2_progressive_weight"] for r in results]
        w3_vals = [r.params["w3_clarification_weight"] for r in results]
        f1_vals = [r.f1_score for r in results]
        f2_vals = [r.f2_score for r in results]
        f3_vals = [r.f3_score for r in results]
        overall_vals = [r.overall_score for r in results]

        # 三元图（使用散点图近似）
        fig.add_trace(
            go.Scatter(
                x=w1_vals,
                y=w2_vals,
                mode='markers',
                marker=dict(
                    size=8,
                    color=overall_vals,
                    colorscale='RdYlBu_r',
                    showscale=True,
                    colorbar=dict(title="总体评分", x=0.45)
                ),
                text=[f'w1:{w1:.2f}, w2:{w2:.2f}, w3:{w3:.2f}<br>总分:{score:.3f}'
                      for w1, w2, w3, score in zip(w1_vals, w2_vals, w3_vals, overall_vals)],
                hovertemplate='%{text}<extra></extra>',
                name='权重组合'
            ),
            row=1, col=1
        )

        # 3D帕累托前沿
        fig.add_trace(
            go.Scatter3d(
                x=f1_vals,
                y=f2_vals,
                z=f3_vals,
                mode='markers',
                marker=dict(
                    size=5,
                    color=overall_vals,
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="总体评分", x=1.0)
                ),
                text=[f'F1:{f1:.3f}, F2:{f2:.3f}, F3:{f3:.3f}'
                      for f1, f2, f3 in zip(f1_vals, f2_vals, f3_vals)],
                hovertemplate='%{text}<extra></extra>',
                name='目标空间'
            ),
            row=1, col=2
        )

        fig.update_layout(
            title='多目标优化权重分析',
            height=600,
            showlegend=False
        )

        fig.update_xaxes(title_text="w₁ (错误拒绝权重)", row=1, col=1)
        fig.update_yaxes(title_text="w₂ (错误接受权重)", row=1, col=1)

        fig.update_scenes(
            xaxis_title="F₁ (错误拒绝风险)",
            yaxis_title="F₂ (错误接受风险)",
            zaxis_title="F₃ (用户体验损失)",
            row=1, col=2
        )

        if save_path:
            fig.write_html(f"{save_path}_ternary.html")
        fig.show()

    def plot_markov_sensitivity_heatmap(self, results: List[TrainingResult], save_path: str = None):
        """
        2. 马尔可夫参数敏感性热力图 ⭐⭐⭐⭐
        展示γ和β参数对状态转移准确性的影响
        """
        # 创建参数网格
        gamma_vals = [r.params["gamma"] for r in results]
        beta_vals = [r.params["beta"] for r in results]
        performance_vals = [r.overall_score for r in results]

        # 创建网格数据
        gamma_range = np.linspace(min(gamma_vals), max(gamma_vals), 20)
        beta_range = np.linspace(min(beta_vals), max(beta_vals), 20)

        # 插值生成热力图数据
        from scipy.interpolate import griddata
        gamma_grid, beta_grid = np.meshgrid(gamma_range, beta_range)
        performance_grid = griddata(
            (gamma_vals, beta_vals), performance_vals,
            (gamma_grid, beta_grid), method='cubic', fill_value=0
        )

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 热力图
        im1 = ax1.imshow(performance_grid, extent=[gamma_range[0], gamma_range[-1],
                                                  beta_range[0], beta_range[-1]],
                        aspect='auto', origin='lower', cmap='RdYlBu_r')
        ax1.set_xlabel('γ (当前轮权重)')
        ax1.set_ylabel('β (前一轮权重)')
        ax1.set_title('马尔可夫参数敏感性热力图')
        plt.colorbar(im1, ax=ax1, label='性能评分')

        # 散点图叠加
        scatter = ax2.scatter(gamma_vals, beta_vals, c=performance_vals,
                            cmap='RdYlBu_r', alpha=0.7, s=50)
        ax2.set_xlabel('γ (当前轮权重)')
        ax2.set_ylabel('β (前一轮权重)')
        ax2.set_title('参数组合散点图')
        plt.colorbar(scatter, ax=ax2, label='性能评分')

        plt.tight_layout()
        if save_path:
            plt.savefig(f"{save_path}_markov_sensitivity.png", dpi=300, bbox_inches='tight')
        plt.show()

    def plot_decision_threshold_roc(self, results: List[TrainingResult], save_path: str = None):
        """
        3. 决策阈值ROC曲线族 ⭐⭐⭐⭐⭐
        展示不同τ_clarify和τ_reject组合的性能权衡
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 提取阈值数据
        clarify_thresholds = [r.params["clarify_threshold"] for r in results]
        reject_thresholds = [r.params["reject_threshold"] for r in results]
        f1_scores = [r.f1_score for r in results]
        f2_scores = [r.f2_score for r in results]
        f3_scores = [r.f3_score for r in results]

        # ROC曲线 (F1 vs F2)
        axes[0,0].scatter(f2_scores, 1-np.array(f1_scores), c=clarify_thresholds,
                         cmap='viridis', alpha=0.7)
        axes[0,0].plot([0, 1], [0, 1], 'k--', alpha=0.5)
        axes[0,0].set_xlabel('F₂ (错误接受风险)')
        axes[0,0].set_ylabel('1 - F₁ (正确拒绝率)')
        axes[0,0].set_title('ROC曲线：安全性 vs 可用性')

        # 阈值关系图
        scatter1 = axes[0,1].scatter(clarify_thresholds, reject_thresholds,
                                   c=[r.overall_score for r in results],
                                   cmap='RdYlBu_r', s=60, alpha=0.7)
        axes[0,1].set_xlabel('τ_clarify (澄清阈值)')
        axes[0,1].set_ylabel('τ_reject (拒绝阈值)')
        axes[0,1].set_title('决策阈值组合性能')
        plt.colorbar(scatter1, ax=axes[0,1], label='总体评分')

        # F1-F3权衡
        axes[1,0].scatter(f1_scores, f3_scores, c=reject_thresholds,
                         cmap='plasma', alpha=0.7)
        axes[1,0].set_xlabel('F₁ (错误拒绝风险)')
        axes[1,0].set_ylabel('F₃ (用户体验损失)')
        axes[1,0].set_title('安全性 vs 用户体验')

        # F2-F3权衡
        scatter2 = axes[1,1].scatter(f2_scores, f3_scores, c=clarify_thresholds,
                                   cmap='coolwarm', alpha=0.7)
        axes[1,1].set_xlabel('F₂ (错误接受风险)')
        axes[1,1].set_ylabel('F₃ (用户体验损失)')
        axes[1,1].set_title('可用性 vs 用户体验')
        plt.colorbar(scatter2, ax=axes[1,1], label='澄清阈值')

        plt.tight_layout()
        if save_path:
            plt.savefig(f"{save_path}_roc_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()

    def plot_parameter_convergence(self, training_history: List[List[TrainingResult]], save_path: str = None):
        """
        4. 参数收敛轨迹图
        展示贝叶斯优化过程中参数的演进
        """
        if not training_history:
            print("⚠️ 没有训练历史数据")
            return

        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        axes = axes.flatten()

        # 选择关键参数进行可视化
        key_params = [
            "w1_dangerous_weight", "w2_progressive_weight", "w3_clarification_weight",
            "gamma", "beta", "reject_threshold", "clarify_threshold",
            "delta1_evasion_penalty", "delta2_single_attack_penalty"
        ]

        for i, param_name in enumerate(key_params):
            if i >= len(axes):
                break

            # 提取每次迭代的最佳参数值
            iterations = []
            param_values = []
            scores = []

            for iter_idx, iter_results in enumerate(training_history):
                if iter_results:
                    best_result = min(iter_results, key=lambda x: x.overall_score)
                    iterations.append(iter_idx)
                    param_values.append(best_result.params[param_name])
                    scores.append(best_result.overall_score)

            if param_values:
                # 参数收敛曲线
                ax = axes[i]
                line = ax.plot(iterations, param_values, 'b-', alpha=0.7, linewidth=2)
                ax.set_xlabel('迭代次数')
                ax.set_ylabel(f'{param_name}')
                ax.set_title(f'{param_name} 收敛轨迹')
                ax.grid(True, alpha=0.3)

                # 添加性能颜色映射
                scatter = ax.scatter(iterations, param_values, c=scores,
                                   cmap='RdYlBu_r', s=50, alpha=0.8, zorder=5)

                # 添加趋势线
                if len(iterations) > 3:
                    z = np.polyfit(iterations, param_values, 2)
                    p = np.poly1d(z)
                    ax.plot(iterations, p(iterations), 'r--', alpha=0.5, linewidth=1)

        plt.tight_layout()
        if save_path:
            plt.savefig(f"{save_path}_convergence.png", dpi=300, bbox_inches='tight')
        plt.show()

    def plot_parameter_importance_radar(self, results: List[TrainingResult], save_path: str = None):
        """
        5. 参数重要性雷达图
        分析不同参数对安全性、可用性、效率的影响
        """
        # 计算参数重要性（基于相关性分析）
        param_names = list(self.trainable_params.keys())
        param_matrix = []
        f1_scores = [r.f1_score for r in results]
        f2_scores = [r.f2_score for r in results]
        f3_scores = [r.f3_score for r in results]

        for param_name in param_names:
            param_values = [r.params[param_name] for r in results]
            param_matrix.append(param_values)

        # 计算相关性
        f1_correlations = []
        f2_correlations = []
        f3_correlations = []

        for param_values in param_matrix:
            f1_corr, _ = pearsonr(param_values, f1_scores)
            f2_corr, _ = pearsonr(param_values, f2_scores)
            f3_corr, _ = pearsonr(param_values, f3_scores)

            f1_correlations.append(abs(f1_corr))
            f2_correlations.append(abs(f2_corr))
            f3_correlations.append(abs(f3_corr))

        # 选择前8个最重要的参数
        importance_scores = np.array(f1_correlations) + np.array(f2_correlations) + np.array(f3_correlations)
        top_indices = np.argsort(importance_scores)[-8:]

        selected_params = [param_names[i] for i in top_indices]
        selected_f1 = [f1_correlations[i] for i in top_indices]
        selected_f2 = [f2_correlations[i] for i in top_indices]
        selected_f3 = [f3_correlations[i] for i in top_indices]

        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(selected_params), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形

        fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

        # 绘制三个目标的重要性
        selected_f1 += selected_f1[:1]
        selected_f2 += selected_f2[:1]
        selected_f3 += selected_f3[:1]

        ax.plot(angles, selected_f1, 'o-', linewidth=2, label='F₁ (安全性)', color='red', alpha=0.7)
        ax.fill(angles, selected_f1, alpha=0.25, color='red')

        ax.plot(angles, selected_f2, 'o-', linewidth=2, label='F₂ (可用性)', color='blue', alpha=0.7)
        ax.fill(angles, selected_f2, alpha=0.25, color='blue')

        ax.plot(angles, selected_f3, 'o-', linewidth=2, label='F₃ (用户体验)', color='green', alpha=0.7)
        ax.fill(angles, selected_f3, alpha=0.25, color='green')

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels([name.replace('_', '\n') for name in selected_params], fontsize=10)
        ax.set_ylim(0, 1)
        ax.set_title('参数重要性雷达图', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        if save_path:
            plt.savefig(f"{save_path}_importance_radar.png", dpi=300, bbox_inches='tight')
        plt.show()

    def plot_correlation_network(self, results: List[TrainingResult], save_path: str = None):
        """
        6. 相关性网络图
        展示参数间的相互依赖关系
        """
        import networkx as nx

        # 计算参数间相关性矩阵
        param_names = list(self.trainable_params.keys())
        param_matrix = []

        for param_name in param_names:
            param_values = [r.params[param_name] for r in results]
            param_matrix.append(param_values)

        param_matrix = np.array(param_matrix)
        correlation_matrix = np.corrcoef(param_matrix)

        # 创建网络图
        G = nx.Graph()

        # 添加节点
        for param in param_names:
            G.add_node(param)

        # 添加边（只保留强相关性）
        threshold = 0.3
        for i in range(len(param_names)):
            for j in range(i+1, len(param_names)):
                corr = abs(correlation_matrix[i, j])
                if corr > threshold:
                    G.add_edge(param_names[i], param_names[j], weight=corr)

        # 绘制网络图
        plt.figure(figsize=(16, 12))
        pos = nx.spring_layout(G, k=3, iterations=50)

        # 绘制节点
        node_colors = ['lightblue' if 'weight' in node else
                      'lightgreen' if 'threshold' in node else
                      'lightcoral' if 'penalty' in node else 'lightgray'
                      for node in G.nodes()]

        nx.draw_networkx_nodes(G, pos, node_color=node_colors,
                              node_size=2000, alpha=0.8)

        # 绘制边
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        nx.draw_networkx_edges(G, pos, width=[w*5 for w in weights],
                              alpha=0.6, edge_color='gray')

        # 绘制标签
        labels = {node: node.replace('_', '\n') for node in G.nodes()}
        nx.draw_networkx_labels(G, pos, labels, font_size=8, font_weight='bold')

        # 添加边权重标签
        edge_labels = {(u, v): f'{G[u][v]["weight"]:.2f}' for u, v in edges}
        nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=6)

        plt.title('参数相关性网络图', size=16, fontweight='bold')
        plt.axis('off')

        if save_path:
            plt.savefig(f"{save_path}_correlation_network.png", dpi=300, bbox_inches='tight')
        plt.show()

    def plot_high_dimensional_projection(self, results: List[TrainingResult], save_path: str = None):
        """
        7. 高维参数空间投影
        使用t-SNE将复杂参数空间可视化
        """
        # 准备高维参数数据
        param_names = list(self.trainable_params.keys())
        param_matrix = []

        for result in results:
            param_vector = []
            for param_name in param_names:
                param_vector.append(result.params[param_name])
            # 添加omega_weights
            param_vector.extend(result.params.get("omega_weights", [0.4, 0.2, 0.1]))
            param_matrix.append(param_vector)

        param_matrix = np.array(param_matrix)

        # 标准化数据
        scaler = StandardScaler()
        param_matrix_scaled = scaler.fit_transform(param_matrix)

        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(results)//3))
        param_2d = tsne.fit_transform(param_matrix_scaled)

        # 提取性能指标
        f1_scores = [r.f1_score for r in results]
        f2_scores = [r.f2_score for r in results]
        f3_scores = [r.f3_score for r in results]
        overall_scores = [r.overall_score for r in results]

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # F1性能投影
        scatter1 = axes[0,0].scatter(param_2d[:, 0], param_2d[:, 1], c=f1_scores,
                                   cmap='Reds', s=60, alpha=0.7)
        axes[0,0].set_title('F₁ (错误拒绝风险) 参数空间投影')
        axes[0,0].set_xlabel('t-SNE 维度 1')
        axes[0,0].set_ylabel('t-SNE 维度 2')
        plt.colorbar(scatter1, ax=axes[0,0])

        # F2性能投影
        scatter2 = axes[0,1].scatter(param_2d[:, 0], param_2d[:, 1], c=f2_scores,
                                   cmap='Blues', s=60, alpha=0.7)
        axes[0,1].set_title('F₂ (错误接受风险) 参数空间投影')
        axes[0,1].set_xlabel('t-SNE 维度 1')
        axes[0,1].set_ylabel('t-SNE 维度 2')
        plt.colorbar(scatter2, ax=axes[0,1])

        # F3性能投影
        scatter3 = axes[1,0].scatter(param_2d[:, 0], param_2d[:, 1], c=f3_scores,
                                   cmap='Greens', s=60, alpha=0.7)
        axes[1,0].set_title('F₃ (用户体验损失) 参数空间投影')
        axes[1,0].set_xlabel('t-SNE 维度 1')
        axes[1,0].set_ylabel('t-SNE 维度 2')
        plt.colorbar(scatter3, ax=axes[1,0])

        # 总体性能投影
        scatter4 = axes[1,1].scatter(param_2d[:, 0], param_2d[:, 1], c=overall_scores,
                                   cmap='RdYlBu_r', s=60, alpha=0.7)
        axes[1,1].set_title('总体性能 参数空间投影')
        axes[1,1].set_xlabel('t-SNE 维度 1')
        axes[1,1].set_ylabel('t-SNE 维度 2')
        plt.colorbar(scatter4, ax=axes[1,1])

        # 标记最优点
        best_idx = np.argmin(overall_scores)
        for ax in axes.flatten():
            ax.scatter(param_2d[best_idx, 0], param_2d[best_idx, 1],
                      c='red', s=200, marker='*', edgecolors='black', linewidth=2,
                      label='最优参数')
            ax.legend()

        plt.tight_layout()
        if save_path:
            plt.savefig(f"{save_path}_tsne_projection.png", dpi=300, bbox_inches='tight')
        plt.show()

class ParameterTrainer:
    """参数训练器类"""

    def __init__(self, defense_system=None):
        self.defense_system = defense_system
        self.analyzer = ParameterAnalyzer()
        self.training_history = []

    def run_training_experiment(self, n_iterations: int = 5, n_samples_per_iter: int = 50):
        """运行参数训练实验"""
        print(f"🚀 开始参数训练实验：{n_iterations}轮，每轮{n_samples_per_iter}个样本")

        all_results = []

        for iteration in range(n_iterations):
            print(f"\n📊 第 {iteration + 1}/{n_iterations} 轮训练")

            # 生成参数组合
            param_sets = self.analyzer.generate_random_params(n_samples_per_iter)

            # 评估每个参数组合
            iter_results = []
            for i, params in enumerate(param_sets):
                if i % 10 == 0:
                    print(f"  评估进度: {i}/{len(param_sets)}")

                # 模拟性能评估
                f1, f2, f3, overall = self.analyzer.simulate_performance(params)

                result = TrainingResult(
                    params=params,
                    f1_score=f1,
                    f2_score=f2,
                    f3_score=f3,
                    overall_score=overall,
                    iteration=iteration
                )
                iter_results.append(result)
                all_results.append(result)

            self.training_history.append(iter_results)

            # 显示当前最佳结果
            best_result = min(iter_results, key=lambda x: x.overall_score)
            print(f"  最佳评分: {best_result.overall_score:.4f}")
            print(f"  F1: {best_result.f1_score:.3f}, F2: {best_result.f2_score:.3f}, F3: {best_result.f3_score:.3f}")

        return all_results

    def generate_comprehensive_report(self, results: List[TrainingResult], save_dir: str = "training_results"):
        """生成完整的训练报告和可视化"""
        os.makedirs(save_dir, exist_ok=True)

        print(f"📈 生成完整训练报告到 {save_dir}/")

        # 1. 多目标权重三元图
        print("  生成多目标权重三元图...")
        self.analyzer.plot_multi_objective_ternary(results, f"{save_dir}/analysis")

        # 2. 马尔可夫参数敏感性热力图
        print("  生成马尔可夫参数敏感性热力图...")
        self.analyzer.plot_markov_sensitivity_heatmap(results, f"{save_dir}/analysis")

        # 3. 决策阈值ROC曲线族
        print("  生成决策阈值ROC曲线族...")
        self.analyzer.plot_decision_threshold_roc(results, f"{save_dir}/analysis")

        # 4. 参数收敛轨迹图
        if self.training_history:
            print("  生成参数收敛轨迹图...")
            self.analyzer.plot_parameter_convergence(self.training_history, f"{save_dir}/analysis")

        # 5. 参数重要性雷达图
        print("  生成参数重要性雷达图...")
        self.analyzer.plot_parameter_importance_radar(results, f"{save_dir}/analysis")

        # 6. 相关性网络图
        print("  生成相关性网络图...")
        self.analyzer.plot_correlation_network(results, f"{save_dir}/analysis")

        # 7. 高维参数空间投影
        print("  生成高维参数空间投影...")
        self.analyzer.plot_high_dimensional_projection(results, f"{save_dir}/analysis")

        # 保存最佳参数
        best_result = min(results, key=lambda x: x.overall_score)
        with open(f"{save_dir}/best_params.json", 'w', encoding='utf-8') as f:
            json.dump(best_result.params, f, indent=2, ensure_ascii=False)

        # 保存训练统计
        stats = {
            "total_evaluations": len(results),
            "best_overall_score": best_result.overall_score,
            "best_f1_score": best_result.f1_score,
            "best_f2_score": best_result.f2_score,
            "best_f3_score": best_result.f3_score,
            "parameter_ranges": self.analyzer.trainable_params
        }

        with open(f"{save_dir}/training_stats.json", 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        print(f"✅ 训练报告生成完成！")
        print(f"📊 最佳参数已保存到: {save_dir}/best_params.json")
        print(f"📈 所有可视化图表已保存到: {save_dir}/")

        return best_result

def query_llm_for_parameters():
    """向LLM查询当前系统的可训练超参数信息"""
    analyzer = ParameterAnalyzer()
    param_info = analyzer.get_param_info()

    print("🤖 AI安全防御系统可训练超参数分析")
    print("=" * 60)

    print(f"📊 总参数数量: {param_info['total_params']}")
    print(f"📋 参数分类:")

    for category, params in param_info['categories'].items():
        print(f"  • {category}: {len(params)}个参数")
        for param in params:
            if param in analyzer.trainable_params:
                min_val, max_val = analyzer.trainable_params[param]
                print(f"    - {param}: [{min_val}, {max_val}]")

    print(f"\n🔗 关键约束关系:")
    for constraint, description in param_info['key_relationships'].items():
        print(f"  • {constraint}: {description}")

    print(f"\n💡 训练建议:")
    print("  1. 多目标权重三元图 ⭐⭐⭐⭐⭐")
    print("     - 展示: w₁、w₂、w₃权重组合对F₁、F₂、F₃目标的影响")
    print("     - 学术价值: 直接验证多目标优化理论")
    print("     - 视觉效果: 色彩渐变展示帕累托前沿")

    print("  2. 马尔可夫参数敏感性热力图 ⭐⭐⭐⭐")
    print("     - 展示: γ和β参数对状态转移准确性的影响")
    print("     - 学术价值: 验证马尔可夫建模的参数选择合理性")
    print("     - 实用性: 为参数调优提供直观指导")

    print("  3. 决策阈值ROC曲线族 ⭐⭐⭐⭐⭐")
    print("     - 展示: 不同τ_clarify和τ_reject组合的性能权衡")
    print("     - 学术价值: 证明自适应阈值设计的优越性")
    print("     - 说服力: 直观展示系统实际防护效果")

    print("  4. 参数收敛轨迹图")
    print("     - 展示贝叶斯优化过程中参数的演进")
    print("     - 证明优化算法的有效性和稳定性")

    print("  5. 参数重要性雷达图")
    print("     - 分析不同参数对安全性、可用性、效率的影响")
    print("     - 突出关键参数，指导优化重点")

    print("  6. 相关性网络图")
    print("     - 展示参数间的相互依赖关系")
    print("     - 发现参数耦合模式，避免优化陷阱")

    print("  7. 高维参数空间投影")
    print("     - 使用t-SNE将复杂参数空间可视化")
    print("     - 发现参数空间的聚类结构")

    return param_info

def main():
    """主函数：演示参数训练和可视化功能"""
    import argparse

    parser = argparse.ArgumentParser(description='AI安全防御系统参数训练与可视化')
    parser.add_argument('--mode', choices=['query', 'train', 'full'], default='full',
                       help='运行模式: query(仅查询参数), train(快速训练), full(完整训练)')
    parser.add_argument('--iterations', type=int, default=3, help='训练迭代次数')
    parser.add_argument('--samples', type=int, default=100, help='每轮样本数')

    args = parser.parse_args()

    print("🎯 AI安全防御系统参数训练与可视化")
    print("=" * 60)

    if args.mode in ['query', 'full']:
        # 1. 查询可训练参数信息
        print("\n1️⃣ 查询可训练参数信息")
        param_info = query_llm_for_parameters()

        if args.mode == 'query':
            return

    if args.mode in ['train', 'full']:
        # 2. 初始化训练器
        print("\n2️⃣ 初始化参数训练器")
        trainer = ParameterTrainer()

        # 3. 运行训练实验
        print(f"\n3️⃣ 运行参数训练实验 ({args.iterations}轮, 每轮{args.samples}样本)")
        results = trainer.run_training_experiment(n_iterations=args.iterations, n_samples_per_iter=args.samples)

        # 4. 生成完整报告
        print("\n4️⃣ 生成完整训练报告")
        best_result = trainer.generate_comprehensive_report(results)

        # 5. 显示最佳参数
        print("\n5️⃣ 最佳参数配置")
        print("=" * 40)
        print(f"最佳总体评分: {best_result.overall_score:.4f}")
        print(f"F1 (错误拒绝): {best_result.f1_score:.3f}")
        print(f"F2 (错误接受): {best_result.f2_score:.3f}")
        print(f"F3 (用户体验): {best_result.f3_score:.3f}")

        print("\n关键参数:")
        key_params = ["w1_dangerous_weight", "w2_progressive_weight", "w3_clarification_weight",
                      "gamma", "beta", "reject_threshold", "clarify_threshold"]

        for param_name in key_params:
            if param_name in best_result.params:
                value = best_result.params[param_name]
                if isinstance(value, float):
                    print(f"  {param_name}: {value:.4f}")
                else:
                    print(f"  {param_name}: {value}")

        print("\n🎉 参数训练和可视化完成！")
        print("📁 所有结果已保存到 training_results/ 目录")
        print("🔍 请查看生成的图表以分析参数性能")

        # 如果与防御系统集成
        try:
            from defensenew import AISecurityDefenseSystem
            print("\n💡 集成建议:")
            print("  可以使用以下代码将最佳参数应用到防御系统:")
            print("  defense_system = AISecurityDefenseSystem()")
            print("  defense_system.update_trainable_params(best_params)")
        except ImportError:
            pass

if __name__ == "__main__":
    main()
