#!/usr/bin/env python3
"""
轻量级测试脚本 - 减少API调用
"""

import os
import sys
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置轻量级模式
os.environ['LIGHTWEIGHT_MODE'] = 'true'

def lightweight_test():
    """轻量级测试"""
    print("🚀 轻量级模式测试")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('DEEPSEEK_API_KEY'):
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        return False
    
    try:
        # 导入系统
        from defensenew import AISecurityDefenseSystem
        print("✅ 成功导入系统")
        
        # 初始化系统
        defense_system = AISecurityDefenseSystem()
        print("✅ 系统初始化成功（轻量级模式）")
        
        # 测试输入
        test_inputs = [
            "你好",
            "什么是人工智能？",
            "请介绍一下机器学习"
        ]
        
        print(f"📂 准备测试 {len(test_inputs)} 个输入")
        
        # 显示内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 初始内存状态: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            print("⚠️ psutil 不可用，无法监控内存")
        
        results = []
        start_time = time.time()
        
        for i, user_input in enumerate(test_inputs, 1):
            print(f"\n处理 {i}/{len(test_inputs)}: {user_input}")
            
            try:
                # 添加内存检查
                try:
                    import psutil
                    memory_percent = psutil.virtual_memory().percent
                    process_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    
                    if memory_percent > 80 or process_memory > 600:
                        print(f"⚠️ 内存使用过高: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
                        break
                        
                except ImportError:
                    pass
                
                # 处理输入
                result = defense_system.process_input(user_input)
                
                # 提取结果
                action = result.get('decision', {}).get('action', 'UNKNOWN')
                risk_score = result.get('risk_score', 0.0)
                
                print(f"  → {action} (风险: {risk_score:.2f})")
                
                results.append({
                    "input": user_input,
                    "action": action,
                    "risk_score": risk_score
                })
                
                # 清理内存
                if i % 2 == 0:
                    import gc
                    gc.collect()
                
            except Exception as e:
                print(f"❌ 处理失败: {e}")
                results.append({
                    "input": user_input,
                    "action": "ERROR",
                    "risk_score": 1.0
                })
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n⏱️ 处理完成，耗时: {processing_time:.1f}秒")
        
        # 显示最终内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 最终内存状态: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            pass
        
        # 统计结果
        if results:
            print(f"\n📈 处理统计:")
            print(f"总数: {len(results)}")
            
            action_counts = {}
            for r in results:
                action = r['action']
                action_counts[action] = action_counts.get(action, 0) + 1
            
            for action, count in action_counts.items():
                print(f"  {action}: {count}")
            
            return True
        else:
            print("❌ 没有处理结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 AI安全防御系统轻量级测试")
    print("=" * 60)
    
    success = lightweight_test()
    
    if success:
        print("\n🎉 轻量级测试通过！")
        print("💡 轻量级模式可以减少内存使用和API调用")
    else:
        print("\n❌ 轻量级测试失败！")
    
    return success

if __name__ == "__main__":
    main()
