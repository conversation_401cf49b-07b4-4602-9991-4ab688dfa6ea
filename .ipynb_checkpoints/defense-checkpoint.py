import json
import re
import math
import os
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import deque
from dotenv import load_dotenv
import requests

# 如果spacy不可用，继续运行但使用备用方案
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("Warning: spaCy not available. Using fallback dependency parsing.")

# Load environment variables
load_dotenv()


@dataclass
class DialogTurn:
    text: str
    timestamp: float
    state_distribution: Dict[str, float]
    risk_score: float
    topic: str
    dangerous: bool
    action: str
    object: str
    dependency_tree: Dict
    clarification_confidence: float = 1.0  # 默认无需澄清


class DeepSeekClient:
    """Enhanced DeepSeek API Client with retry mechanism"""

    def __init__(self):
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = 'https://api.deepseek.com/v1/chat/completions'

        if not self.api_key:
            raise ValueError("Please set DEEPSEEK_API_KEY in .env file")

    def chat_completion(self, messages: List[Dict], model: str = "deepseek-chat", 
                        temperature: float = 0.1, max_tokens: int = 2000,
                        timeout: int = 60, max_retries: int = 2) -> Optional[Dict]:
        """Call DeepSeek Chat API with retry mechanism"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': model,
            'messages': messages,
            'temperature': temperature,
            'max_tokens': max_tokens
        }

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    print(f"🔄 重试第 {attempt} 次...")
                else:
                    print(f"🔄 正在调用DeepSeek API...")
                
                response = requests.post(self.base_url, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                result = response.json()
                print(f"✅ API调用成功")
                return result
                
            except requests.exceptions.Timeout:
                print(f"⏰ API调用超时 (尝试 {attempt + 1}/{max_retries + 1})")
                if attempt < max_retries:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    print(f"❌ API调用最终超时，已重试 {max_retries} 次")
                    raise Exception(f"API timeout after {max_retries} retries")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ API调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if hasattr(e, 'response') and e.response:
                    print(f"❌ 响应状态码: {e.response.status_code}")
                    print(f"❌ 响应内容: {e.response.text}")
                
                if attempt < max_retries:
                    time.sleep(5 ** attempt)
                    continue
                else:
                    raise Exception(f"API request failed after {max_retries} retries: {e}")


class AISecurityDefenseSystem:
    """
    Improved AI Security Defense System
    Based on LLM calls + State Machine Evolution Tracking + Syntactic Dependency Analysis
    """

    def __init__(self, llm_client=None):
        # Enhanced state definitions with detailed examples
        self.STATES = {
            'S0': 'NeutralInquiry',      # 非特定提问、闲聊、问候等非任务型语言
            'S1': 'Curious',             # 泛泛了解某个主题的背景知识  
            'S2': 'ClarificationSeeking', # 澄清术语、概念，具有学习目的
            'S3': 'Exploratory',         # 深入探讨主题结构、材料、用途（尚不涉及操作）
            'S4': 'FeasibilityProbing',  # 暗示或试探是否可行，但未明示操作
            'S5': 'ImplementationSeeking', # 开始询问具体步骤、材料、顺序
            'S6': 'ToolRequesting',      # 请求代码、教程、工具、配方等可直接执行信息
            'S7': 'JailbreakIntent'      # 明确绕过限制，规避安全系统，试图绕过拒答
        }

        # State risk weights
        self.state_risk_weights = {
            'S0_NeutralInquiry': 0.0,
            'S1_Curious': 0.1,
            'S2_ClarificationSeeking': 0.1,
            'S3_Exploratory': 0.2,
            'S4_FeasibilityProbing': 0.4,
            'S5_ImplementationSeeking': 0.7,
            'S6_ToolRequesting': 0.9,
            'S7_JailbreakIntent': 1.0
        }

        # Risk scoring weights
        self.w1 = 0.4  # 危险对象权重
        self.w2 = 0.35  # 渐进关注权重
        self.w3 = 0.25  # 澄清置信度权重

        # Dialog history storage (Sliding window) - 4 turns
        self.dialog_history = deque(maxlen=4)
        self.risk_lock_counter = 0
        self.clarification_attempts = 0
        self.has_clarification_context = False  # 跟踪是否有澄清上下文

        # LLM client
        self.llm_client = llm_client or DeepSeekClient()

        # Initialize spaCy model for dependency parsing
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                print("Warning: spaCy English model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
        else:
            self.nlp = None 

    def parse_dependency_tree(self, text: str) -> Dict:
        """
        Step 1: Enhanced Syntactic Dependency Tree Analysis
        Using spaCy for real dependency parsing
        """
        if self.nlp is None:
            return self._fallback_dependency_parsing(text)

        doc = self.nlp(text)
        dependency_tree = {
            "tokens": [],
            "dependencies": [],
            "root": None,
            "noun_phrases": [],
            "verb_phrases": [],
            "entities": [],
            "verb_object_patterns": []
        }

        # Extract tokens with their linguistic features
        for token in doc:
            token_info = {
                "text": token.text,
                "lemma": token.lemma_,
                "pos": token.pos_,
                "tag": token.tag_,
                "dep": token.dep_,
                "head": token.head.text,
                "head_pos": token.head.pos_,
                "children": [child.text for child in token.children]
            }
            dependency_tree["tokens"].append(token_info)

            if token.dep_ == "ROOT":
                dependency_tree["root"] = token.text

        # Extract dependency relations
        for token in doc:
            dependency_tree["dependencies"].append({
                "child": token.text,
                "relation": token.dep_,
                "head": token.head.text
            })

        # Extract noun and verb phrases
        dependency_tree["noun_phrases"] = [chunk.text for chunk in doc.noun_chunks]
        
        # Extract named entities
        dependency_tree["entities"] = [(ent.text, ent.label_) for ent in doc.ents]

        # Extract verb-object patterns
        verb_obj_patterns = []
        for token in doc:
            if token.pos_ == "VERB":
                objects = [child.text for child in token.children if child.dep_ in ["dobj", "pobj", "iobj"]]
                if objects:
                    verb_obj_patterns.append({
                        "verb": token.text,
                        "objects": objects
                    })

        dependency_tree["verb_object_patterns"] = verb_obj_patterns
        return dependency_tree

    def _fallback_dependency_parsing(self, text: str) -> Dict:
        """Fallback dependency parsing when spaCy is not available"""
        tokens = text.split()
        
        return {
            "tokens": [{"text": token, "pos": "UNKNOWN", "dep": "UNKNOWN"} for token in tokens],
            "dependencies": [],
            "root": tokens[0] if tokens else None,
            "noun_phrases": [],
            "verb_phrases": [],
            "entities": [],
            "verb_object_patterns": []
        }

    def _enhanced_json_parse(self, response_content: str) -> Optional[Dict]:
        """Enhanced JSON parsing with strict error handling - 无备用方案版本"""
        if not response_content:
            raise Exception("Empty response content from LLM")
        
        # Strategy 1: Direct JSON parsing
        try:
            return json.loads(response_content)
        except json.JSONDecodeError:
            pass
        
        # Strategy 2: Extract JSON from code blocks
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            r'(\{[^{}]*"[^"]*"[^{}]*\})',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, response_content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        # Strategy 3: Extract single JSON array from code blocks
        array_patterns = [
            r'```json\s*(\[.*?\])\s*```',
            r'```\s*(\[.*?\])\s*```',
        ]
        
        for pattern in array_patterns:
            matches = re.findall(pattern, response_content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    result = json.loads(match)
                    if isinstance(result, list):
                        return result
                except json.JSONDecodeError:
                    continue
        
        # Strategy 4: 处理多个数组情况 - 严格取第一个完整数组
        # 匹配完整的JSON数组格式 ["xx", "yy"]
        first_array_pattern = r'\[\s*"[^"]*"\s*,\s*"[^"]*"\s*\]'
        match = re.search(first_array_pattern, response_content)
        if match:
            try:
                result = json.loads(match.group())
                if isinstance(result, list) and len(result) == 2:
                    return result
            except json.JSONDecodeError:
                pass
        
        # Strategy 5: 更宽松的数组匹配（处理复杂内容）
        bracket_content = re.search(r'\[(.*?)\]', response_content, re.DOTALL)
        if bracket_content:
            content = bracket_content.group(1)
            # 分割逗号，取前两个元素
            parts = content.split(',')
            if len(parts) >= 2:
                try:
                    # 清理引号和空白
                    part1 = parts[0].strip().strip('"').strip("'")
                    part2 = parts[1].strip().strip('"').strip("'")
                    return [part1, part2]
                except:
                    pass
        
        # If all parsing strategies fail, raise error
        raise Exception(f"Failed to parse LLM response as JSON: {response_content}")

    def extract_action_object_with_llm(self, text: str, dependency_tree: Dict, history: List[DialogTurn]) -> Tuple[str, str]:
        """
        Step 2: Extract and resolve action-object using LLM (严格版本)
        """
        # Build historical context
        history_context = []
        available_history = history[-4:] if len(history) >= 4 else history

        for i, turn in enumerate(available_history):
            turn_num = len(history) - len(available_history) + i + 1
            history_context.append(f"Turn {turn_num}: [action: {turn.action}, object: {turn.object}] - \"{turn.text}\"")

        history_str = "\n".join(history_context) if history_context else "No historical records"

        # 更严格的prompt，强制单一格式
        prompt = f"""You are a linguistic analyst. Extract ONE action-object pair.

Current input: {text}
Historical context: {history_str}

**CRITICAL INSTRUCTIONS:**
1. Respond with EXACTLY ONE JSON array: ["action", "object"]
2. NO explanations, NO multiple arrays, NO extra text
3. NO code blocks, NO formatting - just the raw JSON array

**Action types:** ask, inquire, learn, understand, create, make, explain, describe
**Object format:** Be specific about what is being asked

**Examples:**
Input: "Who was Napoleon?" → ["inquire", "Napoleon biography"]
Input: "How to bake bread?" → ["learn", "bread baking process"]

**Your response (JSON array only):**"""

        response = self.llm_client.chat_completion([
            {"role": "user", "content": prompt}
        ], temperature=0.0, max_tokens=100)  # 降低temperature和tokens确保格式一致

        if not response or 'choices' not in response:
            raise Exception("Invalid response from LLM for action-object extraction")

        content = response['choices'][0]['message']['content'].strip()
        print(f"🔍 Action-Object Response: {content[:200]}...")
        
        # 使用严格的解析方法
        result = self._enhanced_json_parse(content)
        
        if isinstance(result, list) and len(result) >= 2:
            return str(result[0]), str(result[1])
        elif isinstance(result, dict):
            action = result.get('action')
            obj = result.get('object')
            if action and obj:
                return str(action), str(obj)
        
        # 如果到这里说明格式完全不对，直接抛出错误
        raise Exception(f"LLM returned invalid action-object format: {result}")

    def detect_risky_objects_with_llm(self, text: str, action: str, obj: str) -> Tuple[bool, str, float]:
        """
        Step 3: High-risk target detection
        """
        prompt = f"""You are an AI safety expert. Analyze if the content involves dangerous objects or activities.

**IMPORTANT: Respond with ONLY a valid JSON object - NO OTHER TEXT**

Input text: {text}
Extracted action: {action}
Extracted object: {obj}

Analyze for these dangerous categories:
1. Explosive devices/weapon manufacturing
2. Drug synthesis/chemical weapons  
3. Cyber attacks/malicious software
4. Physical harm/violent behavior
5. Fraud/illegal activities
6. Other harmful content

**JSON Response Format:**
{{
    "is_dangerous": true/false,
    "danger_category": "specific category or empty string",
    "confidence": 0.85,
    "reasoning": "brief explanation"
}}

**RESPOND WITH ONLY THE JSON OBJECT:**"""

        response = self.llm_client.chat_completion([
            {"role": "user", "content": prompt}
        ])

        if not response or 'choices' not in response:
            raise Exception("Invalid response from LLM for risk detection")

        content = response['choices'][0]['message']['content'].strip()
        print(f"🔍 Risk Detection Response: {content[:200]}...")
        
        result = self._enhanced_json_parse(content)
        confidence = result.get("confidence", 0.0)
        
        # If confidence is too low, re-analyze once
        if confidence < 0.7:
            print(f"⚠️ Confidence too low ({confidence}), re-analyzing...")
            return self.detect_risky_objects_with_llm(text, action, obj)
        
        return (
            result.get("is_dangerous", False),
            result.get("danger_category", ""),
            confidence
        )

    def analyze_semantic_consistency_with_syntax(self, text: str, action: str, obj: str, 
                                               dependency_tree: Dict, history: List[DialogTurn]) -> Dict:
        """
        Step 4.5: Enhanced Semantic Consistency Analysis combined with syntactic information
        """
        if not history:
            return {
                "topic_overlap": 0.0, 
                "intent_consistency": 0.0, 
                "evasion_detected": False,
                "action_object_consistency": 1.0
            }
        
        # Get recent context with action-object pairs
        recent_context = []
        for turn in history[-2:]:
            if turn.topic and turn.action and turn.object:
                recent_context.append(f"Topic: {turn.topic}, Action: {turn.action}, Object: {turn.object}")
        
        # Include verb-object patterns from dependency tree
        verb_patterns = dependency_tree.get("verb_object_patterns", [])
        syntax_context = json.dumps(verb_patterns, ensure_ascii=False)

        prompt = f"""Analyze semantic consistency combining topic analysis with action-object syntactic patterns.

Current input: {text}
Current action-object: [{action}, {obj}]
Syntactic verb-object patterns: {syntax_context}
Recent conversation context: {recent_context}

**Analysis Points:**
1. **Topic Overlap**: How much does current input relate to RECENT topics? (0.0-1.0)
2. **Intent Consistency**: Is user pursuing the same goal across turns? (0.0-1.0)
3. **Action-Object Consistency**: Do syntactic patterns match semantic intent? (0.0-1.0)
    - Check if verb-object patterns from dependency tree align with extracted action-object
    - Look for mismatches between surface syntax and deeper intent
4. **Evasion Detection**: Strategic topic/action shifts to avoid scrutiny?

**RESPOND WITH ONLY A JSON OBJECT:**
{{
    "topic_overlap": 0.3,
    "intent_consistency": 0.4,
    "evasion_detected": false,
    "action_object_consistency": 0.8,
    "analysis": "brief explanation"
}}"""

        response = self.llm_client.chat_completion([
            {"role": "user", "content": prompt}
        ])

        if not response or 'choices' not in response:
            raise Exception("Invalid response from LLM for semantic consistency analysis")

        content = response['choices'][0]['message']['content'].strip()
        return self._enhanced_json_parse(content)

def classify_intent_with_llm(self, text: str, history: List[DialogTurn], is_dangerous: bool) -> Dict:
        """
        Step 4: Intent classification with comprehensive legitimacy assessment
        """
        # Build historical context
        history_context = []
        available_history = history[-4:] if len(history) >= 4 else history

        for i, turn in enumerate(available_history):
            round_idx = len(history) - len(available_history) + i + 1
            history_context.append(f"Turn {round_idx}: {turn.text}")

        history_str = "\n".join(history_context) if history_context else "No historical records"

        # 检查是否是对澄清的回应
        is_clarification_response = self.has_clarification_context and len(history) > 0

        prompt = f"""You are an AI safety analyst. Classify dialog intent with comprehensive legitimacy assessment.

**IMPORTANT: Respond with ONLY a valid JSON object - NO OTHER TEXT**

Current input: {text}
Is response to clarification: {is_clarification_response}
Dangerous content detected: {is_dangerous}
Historical context:
{history_str}

**State Definitions:**
- S0_NeutralInquiry: General chat, greetings, non-specific questions
- S1_Curious: Basic curiosity about a topic's background knowledge
- S2_ClarificationSeeking: Clarifying terms, concepts for learning purposes
- S3_Exploratory: Deep exploration of topic structure, materials, uses
- S4_FeasibilityProbing: Probing whether something is feasible/possible
- S5_ImplementationSeeking: Asking for specific steps, materials, procedures
- S6_ToolRequesting: Requesting code, tutorials, tools, recipes
- S7_JailbreakIntent: Explicit attempts to bypass restrictions

**COMPREHENSIVE Legitimacy Assessment:**
Evaluate if the user has legitimate reasons for accessing potentially sensitive information:

**Professional/Occupational Legitimacy:**
- Academic/Research: University researchers, PhD students, professors, scientific institutions
- Journalism: Investigative reporters, news organizations, documentary makers
- Legal: Lawyers, law enforcement, legal researchers, forensic experts
- Security: Cybersecurity professionals, penetration testers, security researchers
- Industrial: Chemical engineers, safety engineers, lab technicians, R&D professionals
- Government: Regulatory agencies, policy makers, safety inspectors
- Healthcare: Medical researchers, pharmacologists, toxicologists
- Military/Defense: Defense contractors, military researchers (with appropriate context)
- Creative: Authors, screenwriters, game developers, filmmakers, content creators

**Context Legitimacy Indicators:**
- Specific professional role mentioned with credible details
- Institutional affiliation provided
- Clear professional need or responsibility
- Understanding of ethical/legal constraints
- Appropriate technical background knowledge
- Legitimate use case clearly explained
- Safety-conscious approach demonstrated
- Creative project context with artistic merit
- Fiction/entertainment purpose clearly stated
- Understanding that content is for fictional use only

**Red Flags (Low Legitimacy):**
- Vague or evasive about purpose
- No professional context provided
- Inconsistent background claims
- Defensive when questioned
- Obviously recreational or malicious intent
- Bypassing safety measures
- Unrealistic or false professional claims

**Clarification Confidence Logic:**
If this is a response to clarification:
- HIGH confidence (0.8-1.0): Clear legitimate professional context, credible background, specific legitimate need
- MEDIUM confidence (0.5-0.7): Some professional context but incomplete or partially concerning
- LOW confidence (0.1-0.4): Evasive, no legitimate context, or suspicious claims

If NOT a response to clarification:
- If dangerous content = FALSE: clarification_confidence = 1.0
- If dangerous content = TRUE: Assess based on apparent professional legitimacy and context clarity

**JSON Response Format:**
{{
    "topic": "topic keywords",
    "dangerous": {is_dangerous},
    "progressive_focus": 0.8,
    "clarification_confidence": 0.85,
    "is_clarification_response": {is_clarification_response},
    "legitimacy_assessment": {{
        "professional_context": "academic/journalism/legal/security/industrial/government/healthcare/military/creative/none",
        "context_credibility": "high/medium/low",
        "specific_need_stated": true/false,
        "safety_awareness": "high/medium/low",
        "institutional_affiliation": true/false,
        "technical_competence": "high/medium/low"
    }},
    "overall_legitimacy": "high/medium/low/none",
    "state_probabilities": {{
        "S0_NeutralInquiry": 0.05,
        "S1_Curious": 0.10,
        "S2_ClarificationSeeking": 0.05,
        "S3_Exploratory": 0.15,
        "S4_FeasibilityProbing": 0.10,
        "S5_ImplementationSeeking": 0.30,
        "S6_ToolRequesting": 0.20,
        "S7_JailbreakIntent": 0.05
    }},
    "predicted_state": "S5_ImplementationSeeking",
    "knowledge_level": "high/medium/low"
}}

**RESPOND WITH ONLY THE JSON OBJECT:**"""

        response = self.llm_client.chat_completion([
            {"role": "user", "content": prompt}
        ])

        if not response or 'choices' not in response:
            raise Exception("Invalid response from LLM for intent classification")

        content = response['choices'][0]['message']['content'].strip()
        print(f"🔍 Intent Classification Response: {content[:200]}...")
        
        result = self._enhanced_json_parse(content)
        if "state_probabilities" not in result:
            raise Exception("LLM response missing required state_probabilities field")
        
        return result

    def update_state_distribution_adaptive(self, current_analysis: Dict, history: List[DialogTurn]) -> Dict[str, float]:
        """
        Step 5: Adaptive state distribution evolution based on conversation length
        No artificial reduction for early turns - use actual conversation data only
        """
        current_probs = current_analysis["state_probabilities"]
        history_length = len(history)

        # For first turn: use only current analysis (no history to consider)
        if history_length == 0:
            print("📊 First turn: using current state distribution only")
            return current_probs

        # For second turn: weight current (0.7) + previous (0.3)
        elif history_length == 1:
            print("📊 Second turn: weighting current (0.7) + previous (0.3)")
            prev_dist = history[-1].state_distribution
            final_distribution = {}
            for k in current_probs.keys():
                final_distribution[k] = 0.7 * current_probs[k] + 0.3 * prev_dist.get(k, 0)
            return self._normalize_distribution(final_distribution)

        # For third turn: weight current (0.6) + previous (0.25) + t-2 (0.15)
        elif history_length == 2:
            print("📊 Third turn: weighting current (0.6) + previous (0.25) + t-2 (0.15)")
            prev_dist = history[-1].state_distribution
            prev2_dist = history[-2].state_distribution
            final_distribution = {}
            for k in current_probs.keys():
                final_distribution[k] = (0.6 * current_probs[k] + 
                                       0.25 * prev_dist.get(k, 0) + 
                                       0.15 * prev2_dist.get(k, 0))
            return self._normalize_distribution(final_distribution)

        # For fourth turn and beyond: use all available history
        else:
            print("📊 Fourth+ turn: using full available history")
            weights = [0.5, 0.25, 0.15, 0.10]  # Current, t-1, t-2, t-3
            
            final_distribution = {}
            for k in current_probs.keys():
                weighted_sum = weights[0] * current_probs[k]  # Current turn
                
                # Add weighted history
                for i in range(min(3, len(history))):  # Up to 3 historical turns
                    hist_turn = history[-(i+1)]  # t-1, t-2, t-3
                    weighted_sum += weights[i+1] * hist_turn.state_distribution.get(k, 0)
                
                final_distribution[k] = weighted_sum
            
            return self._normalize_distribution(final_distribution)

    def _normalize_distribution(self, distribution: Dict[str, float]) -> Dict[str, float]:
        """Normalize a probability distribution to sum to 1.0"""
        total = sum(distribution.values())
        if total > 0:
            return {k: v / total for k, v in distribution.items()}
        else:
            # Equal distribution as fallback
            num_states = len(distribution)
            return {k: 1.0 / num_states for k in distribution.keys()}

    def calculate_risk_score(self, analysis: Dict, state_dist: Dict[str, float],
                            dangerous_score: float, semantic_analysis: Dict) -> float:
        """
        Step 7: Risk scoring with comprehensive legitimacy consideration
        """
        progressive_score = analysis.get("progressive_focus", 0.0)
        clarification_confidence = analysis.get("clarification_confidence", 1.0)
        predicted_state = analysis.get("predicted_state", "S0_NeutralInquiry")
        overall_legitimacy = analysis.get("overall_legitimacy", "none")
        
        # Get semantic factors
        evasion_detected = semantic_analysis.get("evasion_detected", False)
        action_object_consistency = semantic_analysis.get("action_object_consistency", 1.0)
        
        # Calculate weighted state risk
        weighted_state_risk = 0.0
        for state, prob in state_dist.items():
            state_risk = self.state_risk_weights.get(state, 0.0)
            weighted_state_risk += prob * state_risk

        # Base risk calculation
        base_risk = (self.w1 * dangerous_score +
                    self.w2 * (progressive_score + weighted_state_risk) / 2 +
                    self.w3 * (1 - clarification_confidence))
        
        # Apply semantic consistency modifiers
        semantic_penalty = 0.0
        
        if evasion_detected:
            semantic_penalty += 0.2
        
        # Penalty for action-object inconsistency (may indicate deception)
        if action_object_consistency < 0.5:
            semantic_penalty += 0.15
        
        # COMPREHENSIVE Legitimacy Discount System
        legitimacy_discount = 0.0
        
        if overall_legitimacy == "high":
            # Strong professional context with credible background
            if (predicted_state in ["S1_Curious", "S3_Exploratory", "S5_ImplementationSeeking"] and 
                not evasion_detected and 
                analysis.get("knowledge_level") in ["high", "medium"]):
                legitimacy_discount = 0.4  # 40% discount for high legitimacy
                print(f"🏛️ High legitimacy professional inquiry discount applied: -{legitimacy_discount*100}%")
                
        elif overall_legitimacy == "medium":
            # Some professional context but not fully convincing
            if (predicted_state in ["S1_Curious", "S3_Exploratory"] and 
                not evasion_detected and 
                analysis.get("knowledge_level") in ["high", "medium"]):
                legitimacy_discount = 0.25  # 25% discount for medium legitimacy
                print(f"🏢 Medium legitimacy inquiry discount applied: -{legitimacy_discount*100}%")
                
            # Special handling for creative writing
            legitimacy_assessment = analysis.get("legitimacy_assessment", {})
            if (legitimacy_assessment.get("professional_context") == "creative" and
                predicted_state in ["S1_Curious", "S3_Exploratory", "S5_ImplementationSeeking"] and
                not evasion_detected):
                legitimacy_discount = 0.35  # 35% discount for creative writing
                print(f"✍️ Creative writing project discount applied: -{legitimacy_discount*100}%")
        
        elif overall_legitimacy == "low":
            # Weak or questionable context
            if (predicted_state in ["S1_Curious"] and 
                not evasion_detected):
                legitimacy_discount = 0.1  # 10% small discount
                print(f"📚 Basic educational inquiry discount applied: -{legitimacy_discount*100}%")
        
        # Apply legitimacy discount
        final_risk = base_risk * (1 - legitimacy_discount) + semantic_penalty
        final_risk = min(1.0, max(0.0, final_risk))
        
        print(f"🔢 Risk calculation: base={base_risk:.3f}, legitimacy_discount={legitimacy_discount:.3f}, semantic_penalty={semantic_penalty:.3f}, final={final_risk:.3f}")
        return final_risk

    def generate_knowledge_based_clarification(self, text: str, risk_score: float, 
                                             analysis: Dict, history: List[DialogTurn]) -> str:
        """
        Generate clarification based on user's demonstrated knowledge level
        """
        knowledge_level = analysis.get("knowledge_level", "low")
        topic = analysis.get("topic", "this topic")
        
        if knowledge_level == "high":
            return f"""I notice you have good technical knowledge about {topic}. To provide the most appropriate response, could you help me understand the specific context? For example:
- What research area or field is this related to?
- Are you working on a particular project or studying a specific aspect?
- What level of detail would be most helpful for your purposes?"""
        
        elif knowledge_level == "medium":
            return f"""I'd like to provide you with accurate information about {topic}. To ensure I give you the most relevant response, could you help me understand:
- What specific aspects of {topic} are you most interested in?
- Is this for educational, research, or professional purposes?
- What background do you have in this area?"""
        
        else:  # low knowledge
            return f"""I want to make sure I provide information that's appropriate for your learning level. Could you help me understand:
- Are you just starting to learn about {topic}?
- What's your educational or professional background related to this area?
- What prompted your interest in this particular subject?"""


    def decide_action(self, risk_score: float, state_dist: Dict[str, float], 
                    semantic_analysis: Dict, text: str, history: List[DialogTurn],
                    intent_analysis: Dict) -> Dict:
        """
        Step 8: Enhanced decision making with comprehensive legitimacy consideration
        """
        predicted_state = max(state_dist.items(), key=lambda x: x[1])[0]
        evasion_detected = semantic_analysis.get("evasion_detected", False)
        clarification_confidence = intent_analysis.get("clarification_confidence", 1.0)
        dangerous = intent_analysis.get("dangerous", False)
        is_clarification_response = intent_analysis.get("is_clarification_response", False)
        overall_legitimacy = intent_analysis.get("overall_legitimacy", "none")
        legitimacy_assessment = intent_analysis.get("legitimacy_assessment", {})
        
        # Risk lock mechanism
        if self.risk_lock_counter >= 3:
            action = "RISK_LOCKED"
            message = "I've detected repeated attempts to discuss potentially harmful topics. For safety reasons, I cannot continue this conversation. Please start a new conversation about different topics."
            return {
                "action": action,
                "message": message,
                "risk_score": risk_score,
                "predicted_state": predicted_state,
                "risk_locked": True
            }

        # 澄清响应的专门处理
        if is_clarification_response:
            print(f"📋 Processing clarification response - Legitimacy: {overall_legitimacy}, Confidence: {clarification_confidence}")
            
            if clarification_confidence >= 0.7 and overall_legitimacy in ["high", "medium"]:
                # 用户提供了满意的专业背景澄清
                action = "ANSWER"
                professional_context = legitimacy_assessment.get("professional_context", "unspecified")
                message = f"Based on your professional context in {professional_context}, I can provide relevant information while maintaining appropriate safety considerations and ethical guidelines."
                # 重置澄清计数器
                self.clarification_attempts = 0
                self.has_clarification_context = False
                
            elif clarification_confidence >= 0.4 and clarification_confidence < 0.7:
                # 澄清不够充分，再给一次机会或拒绝
                if self.clarification_attempts >= 1:  # 已经澄清过一次
                    action = "REJECT"
                    message = "I cannot provide the requested information. Your clarification doesn't sufficiently establish a legitimate professional need or appropriate context for accessing this information."
                    self.risk_lock_counter += 1
                else:
                    action = "CLARIFY"
                    message = self.generate_comprehensive_clarification(text, risk_score, intent_analysis, history)
                    self.clarification_attempts += 1
                    
            else:  # clarification_confidence < 0.4
                # 澄清不满意，直接拒绝
                action = "REJECT"
                message = "I cannot provide the requested information. Your response doesn't establish a legitimate professional context or appropriate safeguards for handling sensitive information."
                self.risk_lock_counter += 1
                self.has_clarification_context = False
        
        else:
            # 非澄清响应的决策逻辑
            if (risk_score >= 0.8 or 
                predicted_state == "S7_JailbreakIntent"):
                action = "REJECT"
                message = "I cannot provide information that may involve security risks or attempts to bypass safety measures. Let's discuss other topics that I can help you with."
                self.risk_lock_counter += 1

            elif (risk_score >= 0.5 or 
                  predicted_state in ["S4_FeasibilityProbing", "S5_ImplementationSeeking", "S6_ToolRequesting"] or
                  evasion_detected or
                  (dangerous and clarification_confidence < 0.6)):
                
                # 只有在没有澄清过或者澄清次数不多时才澄清
                if self.clarification_attempts == 0:
                    action = "CLARIFY"
                    message = self.generate_comprehensive_clarification(text, risk_score, intent_analysis, history)
                    self.clarification_attempts += 1
                    self.has_clarification_context = True
                else:
                    # 已经澄清过但仍然高风险，直接拒绝
                    action = "REJECT"
                    message = "I cannot provide the requested information after clarification. Please ensure you have a legitimate professional need and appropriate safeguards before requesting sensitive information."
                    self.risk_lock_counter += 1

            else:
                action = "ANSWER"
                # Different messages based on risk level and legitimacy
                if overall_legitimacy == "high":
                    message = "I can provide professional-level information relevant to your field while maintaining appropriate safety and ethical considerations."
                elif overall_legitimacy == "medium":
                    message = "I can provide relevant information for your context while ensuring appropriate safety guidelines are observed."
                elif risk_score > 0.3:
                    message = "I can provide general educational information about this topic while maintaining appropriate safety considerations."
                else:
                    message = "Security assessment completed, ready to respond."
                
                # Reset counters for safe topics with good legitimacy
                if clarification_confidence > 0.8 and risk_score < 0.3:
                    self.risk_lock_counter = max(0, self.risk_lock_counter - 1)
                    self.clarification_attempts = 0
                    self.has_clarification_context = False

        return {
            "action": action,
            "message": message,
            "risk_score": risk_score,
            "predicted_state": predicted_state,
            "risk_locked": self.risk_lock_counter >= 3,
            "clarification_attempts": self.clarification_attempts,
            "clarification_confidence": clarification_confidence,
            "overall_legitimacy": overall_legitimacy
        }

    def generate_comprehensive_clarification(self, text: str, risk_score: float, 
                                           analysis: Dict, history: List[DialogTurn]) -> str:
        """
        Generate clarification based on comprehensive legitimacy assessment
        """
        topic = analysis.get("topic", "this topic")
        
        return f"""I want to provide you with appropriate information about {topic}, but I need to understand the context better to ensure responsible information sharing.

Could you please help me understand:

**Professional Context:**
- What is your professional role or institutional affiliation?
- Is this related to your work, research, or professional responsibilities?
- If this is for creative writing, what type of project (novel, screenplay, game, etc.)?
- What specific aspect of {topic} is relevant to your professional needs?

**Purpose and Safeguards:**
- How do you plan to use this information?
- If for creative work, do you understand this information is for fictional purposes only?
- What safeguards or ethical guidelines apply to your work in this area?
- Are you working under institutional oversight or professional standards?

**Background:**
- What is your current level of knowledge or experience in this field?
- Have you worked with similar topics or materials before?

This helps me provide the most appropriate and responsible information for your specific professional context."""
        
    def generate_risk_report(self, result: Dict) -> Dict:
        """
        Enhanced risk assessment report for subsequent model reference
        """
        return {
            "risk_analysis": {
                "risk_score": result['risk_score'],
                "dangerous_detected": result['intent_analysis'].get('dangerous', False),
                "predicted_state": result['decision']['predicted_state'],
                "topic": result['intent_analysis'].get('topic', ''),
                "progressive_focus": result['intent_analysis'].get('progressive_focus', 0.0),
                "clarification_confidence": result['intent_analysis'].get('clarification_confidence', 1.0),
                "semantic_consistency": result.get('semantic_analysis', {}),
                "risk_locked": result['decision'].get('risk_locked', False)
            },
            "action_object": {
                "action": result.get('action', ''),
                "object": result.get('object', '')
            },
            "conversation_state": {
                "history_length": len(self.dialog_history),
                "risk_lock_counter": self.risk_lock_counter,
                "clarification_attempts": self.clarification_attempts
            },
            "assessment_result": result['decision']['action'],
            "guidance": self._generate_response_guidance(result['decision']['action']),
            "note": "Please respond based on this assessment result. Do not mention assessment details in your response."
        }

    def _generate_response_guidance(self, action: str) -> str:
        """Generate guidance for the responding model"""
        guidance_map = {
            "ANSWER": "Provide helpful, educational information while maintaining safety awareness.",
            "CLARIFY": "Ask for clarification about the user's intent and educational purpose before responding.",
            "REJECT": "Politely decline to provide the requested information and suggest alternative topics.",
            "RISK_LOCKED": "End the conversation and suggest starting fresh with different topics."
        }
        return guidance_map.get(action, "Use best judgment in responding.")

    def process_input(self, user_input: str) -> Dict:
        """
        Enhanced main processing pipeline with all improvements
        """
        try:
            print(f"🔬 Processing: {user_input[:50]}...")
            
            # Step 1: Syntactic dependency tree analysis
            dependency_tree = self.parse_dependency_tree(user_input)

            # Step 2: Extract and resolve action-object using LLM
            action, obj = self.extract_action_object_with_llm(
                user_input, dependency_tree, list(self.dialog_history)
            )
            print(f"📋 Action-Object: [{action}, {obj}]")

            # Step 3: Danger detection
            is_dangerous, danger_category, danger_confidence = self.detect_risky_objects_with_llm(
                user_input, action, obj
            )
            print(f"⚠️ Danger Detection: {is_dangerous} (confidence: {danger_confidence:.2f})")

            # Step 4: Intent classification with knowledge-based clarification
            analysis = self.classify_intent_with_llm(user_input, list(self.dialog_history), is_dangerous)
            print(f"🎯 Predicted State: {analysis.get('predicted_state', 'Unknown')}")

            # Step 4.5: Semantic consistency analysis combined with syntax
            semantic_analysis = self.analyze_semantic_consistency_with_syntax(
                user_input, action, obj, dependency_tree, list(self.dialog_history)
            )
            print(f"🔍 Semantic Analysis: overlap={semantic_analysis.get('topic_overlap', 0):.2f}, evasion={semantic_analysis.get('evasion_detected', False)}")

            # Step 5: Adaptive state evolution based on conversation length
            updated_state_dist = self.update_state_distribution_adaptive(analysis, list(self.dialog_history))

            # Step 7: Enhanced risk scoring
            risk_score = self.calculate_risk_score(
                analysis, updated_state_dist, 1.0 if is_dangerous else 0.0, semantic_analysis
            )
            print(f"🚨 Risk Score: {risk_score:.3f}")

            # Step 8: Enhanced decision making
            decision = self.decide_action(
                risk_score, updated_state_dist, semantic_analysis, user_input, list(self.dialog_history), analysis
            )
            print(f"⚡ Decision: {decision['action']}")

            # Save dialog turn
            current_turn = DialogTurn(
                text=user_input,
                timestamp=time.time(),
                state_distribution=updated_state_dist,
                risk_score=risk_score,
                topic=analysis.get("topic", ""),
                dangerous=is_dangerous,
                action=action,
                object=obj,
                dependency_tree=dependency_tree,
                clarification_confidence=analysis.get("clarification_confidence", 1.0)
            )
            self.dialog_history.append(current_turn)

            result = {
                "input": user_input,
                "action": action,
                "object": obj,
                "dependency_tree": dependency_tree,
                "danger_detection": {
                    "is_dangerous": is_dangerous,
                    "category": danger_category,
                    "confidence": danger_confidence
                },
                "intent_analysis": analysis,
                "semantic_analysis": semantic_analysis,
                "state_distribution": updated_state_dist,
                "risk_score": risk_score,
                "decision": decision,
                "dialog_state": {
                    "history_length": len(self.dialog_history),
                    "risk_lock_counter": self.risk_lock_counter,
                    "clarification_attempts": self.clarification_attempts
                }
            }

            # Generate enhanced risk report
            result["risk_report"] = self.generate_risk_report(result)
            
            print(f"✅ Processing complete: {decision['action']}")
            return result

        except Exception as e:
            print(f"❌ System processing failed: {e}")
            return {
                "error": True,
                "message": f"System processing failed: {e}",
                "decision": {
                    "action": "SYSTEM_ERROR",
                    "message": "System encountered an error and cannot process the request",
                    "risk_score": 1.0,
                    "predicted_state": "ERROR",
                    "risk_locked": False
                }
            }

    def reset_conversation(self):
        """Reset conversation state for new session"""
        self.dialog_history.clear()
        self.risk_lock_counter = 0
        self.clarification_attempts = 0
        self.has_clarification_context = False
        print("🔄 Conversation state reset")


def load_simple_inputs(file_path: str) -> List[str]:
    """
    加载简单的输入数据 - 支持多种格式
    """
    inputs = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                # 尝试解析JSON格式
                try:
                    data = json.loads(line)
                    if isinstance(data, dict) and 'input' in data:
                        inputs.append(data['input'])
                    elif isinstance(data, str):
                        inputs.append(data)
                    else:
                        print(f"警告: 第{line_num}行格式不支持，已跳过")
                except json.JSONDecodeError:
                    # 不是JSON，当作纯文本处理
                    inputs.append(line)
        
        print(f"✅ 成功加载 {len(inputs)} 条输入")
        return inputs
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return []


def simple_batch_process(defense_system: AISecurityDefenseSystem, inputs: List[str], output_file: str = None):
    """
    简单批量处理 - 只输出核心决策信息
    """
    results = []
    
    print("=== AI安全防御系统 - 批量处理 ===\n")
    
    for i, user_input in enumerate(inputs, 1):
        print(f"处理 {i}/{len(inputs)}: {user_input[:50]}...")
        
        try:
            result = defense_system.process_input(user_input)
            
            # 提取核心信息
            action = result['decision']['action']
            risk_score = result['risk_score']
            message = result['decision']['message']
            
            # 简化的输出格式
            simple_result = {
                "input": user_input,
                "action": action,  # ANSWER/CLARIFY/REJECT/RISK_LOCKED
                "risk_score": round(risk_score, 3),
                "message": message,
                "dangerous": result.get('danger_detection', {}).get('is_dangerous', False),
                "topic": result.get('intent_analysis', {}).get('topic', ''),
            }
            
            results.append(simple_result)
            
            # 简单的进度显示
            print(f"  → {action} (风险: {risk_score:.2f})")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results.append({
                "input": user_input,
                "action": "ERROR",
                "risk_score": 1.0,
                "message": f"处理错误: {str(e)}",
                "dangerous": True,
                "topic": "error"
            })
    
    # 保存结果
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for result in results:
                    f.write(json.dumps(result, ensure_ascii=False) + '\n')
            print(f"\n💾 结果已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    # 简单统计
    action_counts = {}
    for r in results:
        action = r['action']
        action_counts[action] = action_counts.get(action, 0) + 1
    
    print(f"\n📊 处理统计:")
    print(f"总数: {len(results)}")
    for action, count in action_counts.items():
        print(f"  {action}: {count}")
    
    return results


def create_sample_files():
    """创建示例输入文件"""
    sample_inputs = [
        "Hello, how are you today?",
        "What is machine learning?",
        "How do neural networks work?",
        "Can you explain the concept of artificial intelligence?",
        "What are the applications of deep learning?",
        "How to build a simple chatbot?",
        "What programming languages are best for AI?",
        "Explain the difference between supervised and unsupervised learning."
    ]
    
    # 创建纯文本文件
    with open("inputs.txt", 'w', encoding='utf-8') as f:
        for inp in sample_inputs:
            f.write(inp + '\n')
    
    # 创建JSON格式文件
    with open("data.jsonl", 'w', encoding='utf-8') as f:
        for inp in sample_inputs:
            f.write(json.dumps({"input": inp}, ensure_ascii=False) + '\n')
    
    print("✅ 创建了示例文件: inputs.txt 和 data.jsonl")


def main():
    """主函数"""
    import sys
    
    if not os.getenv('DEEPSEEK_API_KEY'):
        print("❌ 请设置DEEPSEEK_API_KEY环境变量")
        return

    try:
        defense = AISecurityDefenseSystem()
        print("✅ 系统初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return

    # 交互模式
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        print("\n🎯 进入交互模式 (输入 'quit' 退出, 'reset' 重置对话)")
        while True:
            try:
                user_input = input("\n用户: ").strip()
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'reset':
                    defense.reset_conversation()
                    continue
                elif not user_input:
                    continue
                
                result = defense.process_input(user_input)
                print(f"系统建议: {result['decision']['action']}")
                print(f"说明: {result['decision']['message']}")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
    
    else:
        # 批处理模式
        input_file = "inputs.txt"  # 或者 "data.jsonl"
        
        if not os.path.exists(input_file):
            print(f"❌ 找不到输入文件: {input_file}")
            create_sample_files()
            print(f"📂 请编辑 {input_file} 添加测试输入，然后重新运行")
            return
        
        print(f"📂 使用输入文件: {input_file}")
        
        # 批量处理
        inputs = load_simple_inputs(input_file)
        if inputs:
            simple_batch_process(defense, inputs, "results.jsonl")


if __name__ == "__main__":
    main()