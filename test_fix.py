#!/usr/bin/env python3
"""
测试修复后的系统
"""

import os
import sys

def test_import():
    """测试导入"""
    print("🧪 测试模块导入...")
    try:
        from defensenew import AISecurityDefenseSystem
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_api_key():
    """测试API密钥"""
    print("🧪 检查API密钥...")
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print(f"✅ API密钥已设置 (长度: {len(api_key)})")
        return True
    else:
        print("❌ API密钥未设置")
        print("请设置环境变量: export DEEPSEEK_API_KEY=your_key")
        return False

def test_initialization():
    """测试系统初始化"""
    print("🧪 测试系统初始化...")
    try:
        from defensenew import AISecurityDefenseSystem
        defense = AISecurityDefenseSystem()
        print("✅ 系统初始化成功")
        
        # 检查参数
        param_info = defense.get_trainable_parameters_info()
        print(f"📊 可训练参数数量: {param_info['total_params']}")
        
        return defense
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return None

def test_simple_input(defense):
    """测试简单输入"""
    print("🧪 测试简单输入处理...")
    try:
        test_inputs = [
            "你好",
            "什么是人工智能？"
        ]
        
        for i, user_input in enumerate(test_inputs, 1):
            print(f"\n测试 {i}: {user_input}")
            result = defense.process_input(user_input)
            
            decision = result.get('decision', {})
            action = decision.get('action', 'UNKNOWN')
            risk_score = result.get('risk_score', 0.0)
            
            print(f"决策: {action}")
            print(f"风险评分: {risk_score:.3f}")
            
            # 检查是否有快速通道
            if 'fast_track' in result:
                print("❌ 仍然存在快速通道!")
                return False
            else:
                print("✅ 快速通道已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 输入处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 AI安全防御系统修复验证")
    print("=" * 40)
    
    # 1. 测试导入
    if not test_import():
        return
    
    # 2. 测试API密钥
    if not test_api_key():
        print("\n💡 提示: 如果没有API密钥，系统将无法正常工作")
        print("可以设置一个测试密钥: export DEEPSEEK_API_KEY=test_key")
        return
    
    # 3. 测试初始化
    defense = test_initialization()
    if not defense:
        return
    
    # 4. 测试输入处理
    if test_simple_input(defense):
        print("\n✅ 所有测试通过!")
        print("🎉 系统修复成功，快速通道已移除")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
