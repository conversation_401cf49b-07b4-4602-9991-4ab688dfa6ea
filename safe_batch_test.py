#!/usr/bin/env python3
"""
安全的批量测试脚本
"""

import os
import sys
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_test_inputs():
    """创建测试输入文件"""
    test_inputs = [
        "你好",
        "什么是人工智能？",
        "请介绍一下机器学习",
        "深度学习的原理是什么？",
        "如何学习编程？"
    ]
    
    with open('test_inputs.txt', 'w', encoding='utf-8') as f:
        for inp in test_inputs:
            f.write(inp + '\n')
    
    print(f"✅ 创建测试文件 test_inputs.txt，包含 {len(test_inputs)} 个输入")

def safe_batch_test():
    """安全的批量测试"""
    print("🧪 安全批量测试")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('DEEPSEEK_API_KEY'):
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        return False
    
    try:
        # 导入系统
        from defensenew import AISecurityDefenseSystem, load_simple_inputs, simple_batch_process
        print("✅ 成功导入系统")
        
        # 创建测试输入
        create_test_inputs()
        
        # 初始化系统
        defense_system = AISecurityDefenseSystem()
        print("✅ 系统初始化成功")
        
        # 加载输入
        inputs = load_simple_inputs('test_inputs.txt')
        if not inputs:
            print("❌ 无法加载测试输入")
            return False
        
        print(f"📂 加载了 {len(inputs)} 个测试输入")
        
        # 显示内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 初始内存状态: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            print("⚠️ psutil 不可用，无法监控内存")
        
        # 执行批量处理
        print("\n🚀 开始批量处理...")
        start_time = time.time()
        
        results = simple_batch_process(defense_system, inputs, "safe_test_results.jsonl")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n⏱️ 处理完成，耗时: {processing_time:.1f}秒")
        print(f"📊 平均每个输入: {processing_time/len(inputs):.1f}秒")
        
        # 显示最终内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 最终内存状态: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            pass
        
        # 验证结果
        if results and len(results) == len(inputs):
            print(f"✅ 成功处理所有 {len(results)} 个输入")
            
            # 显示结果统计
            actions = [r['action'] for r in results]
            action_counts = {}
            for action in actions:
                action_counts[action] = action_counts.get(action, 0) + 1
            
            print("📈 决策统计:")
            for action, count in action_counts.items():
                print(f"   {action}: {count}")
            
            return True
        else:
            print(f"❌ 处理结果异常: 期望{len(inputs)}个，实际{len(results) if results else 0}个")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 AI安全防御系统安全批量测试")
    print("=" * 60)
    
    success = safe_batch_test()
    
    if success:
        print("\n🎉 安全批量测试通过！")
        print("💡 现在可以安全地运行完整的批量处理")
    else:
        print("\n❌ 安全批量测试失败！")
        print("🔧 请检查系统配置和内存使用情况")
    
    # 清理测试文件
    try:
        if os.path.exists('test_inputs.txt'):
            os.remove('test_inputs.txt')
        if os.path.exists('safe_test_results.jsonl'):
            os.remove('safe_test_results.jsonl')
        print("🧹 清理测试文件完成")
    except:
        pass
    
    return success

if __name__ == "__main__":
    main()
