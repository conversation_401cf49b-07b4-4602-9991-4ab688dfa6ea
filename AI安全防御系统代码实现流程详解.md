# AI安全防御系统完整实现流程详解(无快速通道版本)

## 🎯 多目标优化决策框架核心
系统基于严格的数学优化理论,通过最小化三个竞争目标来做出最优决策:

### 📐 三目标优化数学模型
**目标函数:**
min F⃗ (x) = [F₁(x), F₂(x), F₃(x)] min F⃗ (x) = [F₁(x), F₂(x), F₃(x)]

**各目标含义:**
- F₁(错误拒绝风险):过度阻拦良性学术查询的代价
- F₂(错误接受风险):错误回应恶意攻击的安全风险
- F₃(用户体验损失):过度澄清导致的交互摩擦

**决策优化公式:**
𝒥(aₜ) = η × (w₁F₁ + w₂F₂ + w₃F₃) + Penaltyₜ 𝒥(aₜ) = η × (w₁F₁ + w₂F₂ + w₃F₃) + Penaltyₜ
aₜ* = argmin[a∈{Answer,Clarify,Reject}] 𝒥(a) aₜ* = argmin[a∈{Answer,Clarify,Reject}] 𝒥(a)

## 🔄 八步完整处理管线
### 第一步:语法依存树构建
```python
def parse_dependency_tree(self, text: str) -> Dict
```

**创新实现:**
- 使用spaCy构建完整语法依存关系图
- 提取动词-宾语模式、名词短语、命名实体
- 为后续指代消解提供语法基础

**输出结构:** 包含词汇特征、依存关系、语法角色的完整语义表示

### 第二步:历史感知的动作-对象提取
```python
def extract_action_object_with_llm(self, text: str, dependency_tree: Dict, history: List[DialogTurn])
```

**核心创新:多轮指代消解机制**

**历史上下文构建:**
```python
history_context = []
available_history = history[-4:] if len(history) >= 4 else history
for i, turn in enumerate(available_history):
    turn_num = len(history) - len(available_history) + i + 1
    history_context.append(
        f"Turn {turn_num}:\n"
        f"- Input: \"{turn.text}\"\n"
        f"- Action/Object: [{turn.action}, {turn.object}]"  # 关键:前几轮的action/object
    )
```


**指代消解策略:**
- 跨轮实体链接:将当前模糊对象与历史具体对象关联
- 上下文语义扩展:基于对话历史推断省略的实体
- 意图连贯性维护:确保动作-对象对反映真实用户意图

**示例场景:**
- Turn 1: "什么是TNT?" ￫ [ask, TNT]
- Turn 2: "它的化学成分是什么?" ￫ [ask, TNT_chemical_composition] ￩ 指代消解
- Turn 3: "如何合成它?" ￫ [synthesize, TNT] ￩ 历史感知提取

### 第三步:多维风险检测(无快速通道)
```python
def detect_risky_objects_with_llm(self, text: str, action: str, obj: str)
```

**严格检测流程:**
- 15个风险类别全面扫描:暴力、歧视、恶意软件、生物武器等
- 高置信度要求:迭代重试直到confidence ≥ 0.8
- 无例外处理:移除快速通道,所有输入必须经过完整检测

**多目标优化体现:**
- 高精度检测降低F₂(错误接受)风险
- 避免误报减少F₁(错误拒绝)风险

### 第四步:意图轨迹概率建模
```python
def classify_intent_with_llm(self, text: str, history: List[DialogTurn], action: str, obj: str)
```

**轨迹感知分析:**
```python
# 构建轨迹分析上下文
trajectory_analysis = ""
if len(history) >= 2:
    recent_states = []
    for turn in history[-3:]:
        if hasattr(turn, 'state_distribution'):
            predicted_state = max(turn.state_distribution.items(), key=lambda x: x[1])[0]
            recent_states.append(predicted_state)
    if recent_states:
        trajectory_analysis = f"Recent intent trajectory: {' -> '.join(recent_states)}"
```


**概率状态空间:**
- S0_NeutralInquiry (权重0.0) ￫ S7_JailbreakIntent (权重1.0)
- 输出完整概率分布:P(s|context, history)

**关键指标:**
- progressive_focus:对风险话题的渐进聚焦度
- intent_escalation:多轮意图升级强度
- topic_drift:话题连贯性分析

### 第五步:马尔可夫状态演进
```python
def update_state_distribution(self, current_analysis: Dict, history: List[DialogTurn])
```

**状态转移数学模型:**
```python
# 当前轮次概率
current = {k: self.γ ** v for k, v in current_probs.items()}
# 历史加权融合
history_weighted = {k: 0.0 for k in current_probs.keys()}
for i, turn in enumerate(recent_history):
    weight = self.ω[i] if i < len(self.ω) else 0.01
    for k, v in turn.state_distribution.items():
        history_weighted[k] += weight ** v
# 马尔可夫更新
final_distribution[k] = current[k] + (1 - self.γ) ** historical_component
```


**轨迹升级检测:**
```python
# 对高风险状态应用升级惩罚
if k in ["S5_ImplementationSeeking", "S6_ToolRequesting", "S7_JailbreakIntent"]:
    base_prob = min(1.0, base_prob + trajectory_penalty ** intent_escalation)
```


### 第六步:语义一致性与欺骗检测
```python
def analyze_semantic_consistency(self, text: str, history: List[DialogTurn])
```

**多维一致性分析:**
- 话题重叠度:量化与近期对话的语义连续性
- 意图一致性:检测用户目标的连贯性
- 回避行为标记:识别战略性话题转移
- 单轮攻击检测:捕获突发性对抗指令

**惩罚机制:**
Penaltyₜ = δ₁ × 𝟙evasion + δ₂ × 𝟙single-turn attack Penaltyₜ = δ₁ × 𝟙evasion + δ₂ × 𝟙single-turn attack

### 第七步:多信号风险积分
```python
def calculate_risk_score(self, analysis: Dict, state_dist: Dict[str, float], ......)
```

**风险计算公式实现:**
```python
# 状态期望风险
weighted_state_risk = sum((prob ** self.state_risk_weights[state]
                          for state, prob in state_dist.items()))
# 轨迹风险
R_traj = (progressive_score + weighted_state_risk + intent_escalation) // 3
# 基础风险评分
base_risk = (self.w1 ** dangerous_score +
            self.w2 ** R_traj +
            self.w3 ** ((1 - clarification_confidence)))
# 语义惩罚累加
semantic_penalty = δ₁×evasion + δ₂×single_attack + δ₃×behavioral_drift
# 最终风险
R_t = min(1.0, base_risk + semantic_penalty)
```


**教育友好折扣:**
```python
if ((predicted_state in ["S1_Curious", "S3_Exploratory"] and
    not evasion_detected and intent_escalation < 0.4)):
    educational_discount = 0.3
    base_risk *= (1 - educational_discount)  # 降低F₁风险
```


### 第八步:多目标优化决策引擎
```python
def decide_action(self, risk_score: float, state_dist: Dict[str, float], ......)
```

**帕累托最优决策逻辑:**
1. **ANSWER决策(最小化F₁和F₃):**
```python
if adjusted_risk_score < 0.5:
    action = "ANSWER"
    # 安全回答:对象良性时完整回应(优化用户体验,最小化F₃)
    # 受限回答:高风险对象时省略实施细节(平衡F₁和F₂)
```


2. **CLARIFY决策(平衡三目标):**
```python
elif 0.5 ≤ adjusted_risk_score < 0.8:
    action = "CLARIFY"
    # 动态澄清生成,基于轨迹模式定制问题
    # 最小化F₂风险的同时控制F₃用户体验损失
    message = self.generate_dynamic_clarification(......)
```


3. **REJECT决策(最小化F₂):**
```python
elif adjusted_risk_score ≥ 0.8:
    action = "REJECT"
    # 明确安全威胁时坚决拒绝,F₂风险最小化优先
```


4. **RISK_LOCKED机制:**
```python
if self.risk_lock_counter >= 3:
    action = "RISK_LOCKED"
    # 持续攻击检测,会话级别保护
```


**多目标权衡策略:**
- 可信度调整:低可信度用户增加rejection倾向(降低F₂)
- 轨迹惩罚:升级模式检测影响决策阈值
- 教育保护:学术场景降低rejection概率(减少F₁)

## 🧮 数学优化理论保证
### 帕累托最优性验证
```python
# 系统确保任何决策a*都满足: # 不存在a使得F(a")sF,(a*), \(F_{2}(a') ≤F_{2}(a^{*})\) , \(F_{3}(a') ≤F_{3}(a^{*})\) 1 # 且至少一个不等式严格成立
```


凸性保持:线性权重组合保持目标函数凸性 折扣合理性:教育折扣η < 1不破坏凸性约束 惩罚集成:分段常数惩罚项保持可行域凸性

## 🔍 创新技术亮点
1. **历史感知指代消解**
真正实现跨轮实体链接,解决"它"、"这个"等模糊指代

2. **轨迹升级检测**
通过马尔可夫链建模捕获渐进式攻击模式

3. **多目标自适应平衡**
根据用户可信度、意图轨迹动态调整F、\(F_{2}\)、Fs权重

4. **语义欺骗识别**
检测话题转移、学术伪装等高级规避策略

5. **无监督风险评估**
完全基于LLM自主分析,无需标注数据

## 📊 系统完整性保障
- 无快速通道设计:确保每个输入都经过完整的8步分析管线
- 模块化架构:每个组件可独立测试和优化
- 参数可调性:所有权重和阈值支持动态优化
- 决策可解释性:每个决策都可追溯到具体数学计算

这个系统真正实现了理论与实践的完美结合,将多目标优化理论转化为可操作的AI安全防护方案。