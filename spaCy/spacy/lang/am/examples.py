"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.am.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "አፕል የዩኬን ጅምር ድርጅት በ 1 ቢሊዮን ዶላር ለመግዛት አስቧል።",
    "የራስ ገዝ መኪኖች የኢንሹራንስ ኃላፊነትን ወደ አምራቾች ያዛውራሉ",
    "ሳን ፍራንሲስኮ የእግረኛ መንገድ አቅርቦት ሮቦቶችን ማገድን ይመለከታል",
    "ለንደን በእንግሊዝ የምትገኝ ትልቅ ከተማ ናት።",
    "የት ነህ?",
    "የፈረንሳይ ፕሬዝዳንት ማናቸው?",
    "የአሜሪካ ዋና ከተማ ምንድነው?",
    "ባራክ ኦባማ መቼ ተወለደ?",
]
