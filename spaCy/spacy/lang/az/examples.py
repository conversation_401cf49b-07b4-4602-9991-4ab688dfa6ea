"""
Example sentences to test spaCy and its language models.
>>> from spacy.lang.az.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Bu bir cümlədir.",
    "Necəsən?",
    "Qarabağ ordeni vətən müharibəsində qələbə münasibəti ilə təsis edilmişdir.",
    "Məktəbimizə Bakıdan bir tarix müəllimi gəlmişdi.",
    "Atılan növbəti mərmilər lap yaxınlıqda partladı.",
    "Sinqapur koronavirus baxımından ən təhlükəsiz ölkələr sırasındadır.",
    "Marsda ilk sınaq uçuşu həyata keçirilib.",
    "SSRİ dağılandan bəri 5 sahil dövləti Xəzərin statusunu müəyyən edə bilməyiblər.",
    "Videoda beyninə xüsusi çip yerləşdirilmiş meymun əks olunub.",
]
