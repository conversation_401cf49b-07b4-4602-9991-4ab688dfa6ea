"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.ar.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    "نال الكاتب خالد توفيق  جائزة الرواية العربية في معرض الشارقة الدولي للكتاب",
    "أين تقع دمشق ؟",
    "كيف حالك ؟",
    "هل يمكن ان نلتقي على الساعة الثانية عشرة ظهرا ؟",
    "ماهي أبرز التطورات السياسية، الأمنية والاجتماعية في العالم ؟",
    "هل بالإمكان أن نلتقي غدا؟",
    "هناك نحو 382 مليون شخص مصاب بداء السكَّري في العالم",
    "كشفت دراسة حديثة أن الخيل تقرأ تعبيرات الوجه وتستطيع أن تتذكر مشاعر الناس وعواطفهم",
]
