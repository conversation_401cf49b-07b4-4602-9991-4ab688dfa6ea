# Source: https://github.com/taranjeet/hindi-tokenizer/blob/master/stopwords.txt, https://data.mendeley.com/datasets/bsr3frvvjc/1#file-a21d5092-99d7-45d8-b044-3ae9edd391c6

STOP_WORDS = set(
    """
अंदर
अत
अदि
अप
अपना
अपनि
अपनी
अपने
अभि
अभी
अंदर
आदि
आप
अगर
इंहिं
इंहें
इंहों
इतयादि
इत्यादि
इन
इनका
इन्हीं
इन्हें
इन्हों
इस
इसका
इसकि
इसकी
इसके
इसमें
इसि
इसी
इसे
उंहिं
उंहें
उंहों
उन
उनका
उनकि
उनकी
उनके
उनको
उन्हीं
उन्हें
उन्हों
उस
उसके
उसि
उसी
उसे
एक
एवं
एस
एसे
ऐसे
ओर
और
कइ
कई
कर
करता
करते
करना
करने
करें
कहते
कहा
का
काफि
काफ़ी
कि
किंहें
किंहों
कितना
किन्हें
किन्हों
किया
किर
किस
किसि
किसी
किसे
की
कुछ
कुल
के
को
कोइ
कोई
कोन
कोनसा
कौन
कौनसा
गया
घर
जब
जहाँ
जहां
जा
जिंहें
जिंहों
जितना
जिधर
जिन
जिन्हें
जिन्हों
जिस
जिसे
जीधर
जेसा
जेसे
जैसा
जैसे
जो
तक
तब
तरह
तिंहें
तिंहों
तिन
तिन्हें
तिन्हों
तिस
तिसे
तो
था
थि
थी
थे
दबारा
दवारा
दिया
दुसरा
दुसरे
दूसरे
दो
द्वारा
न
नहिं
नहीं
ना
निचे
निहायत
नीचे
ने
पर
पहले
पुरा
पूरा
पे
फिर
बनि
बनी
बहि
बही
बहुत
बाद
बाला
बिलकुल
भि
भितर
भी
भीतर
मगर
मानो
मे
में
मैं
मुझको
मेरा
यदि
यह
यहाँ
यहां
यहि
यही
या
यिह
ये
रखें
रवासा
रहा
रहे
ऱ्वासा
लिए
लिये
लेकिन
व
वगेरह
वग़ैरह
वरग
वर्ग
वह
वहाँ
वहां
वहिं
वहीं
वाले
वुह
वे
वग़ैरह
संग
सकता
सकते
सबसे
सभि
सभी
साथ
साबुत
साभ
सारा
से
सो
संग
हि
ही
हुअ
हुआ
हुइ
हुई
हुए
हे
हें
है
हैं
हो
हूँ
होता
होति
होती
होते
होना
होने
""".split()
)
