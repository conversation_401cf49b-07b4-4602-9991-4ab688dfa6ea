from ...attrs import LIKE_NUM

_num_words = [
    "nul",
    "jedyn",
    "jedna",
    "jedne",
    "dwaj",
    "dwě",
    "tři",
    "třo",
    "štyri",
    "<PERSON>ty<PERSON><PERSON>",
    "pjeć",
    "š<PERSON><PERSON><PERSON>",
    "sydom",
    "wosom",
    "dźew<PERSON><PERSON>",
    "dź<PERSON><PERSON><PERSON>",
    "jědna<PERSON>e",
    "dwana<PERSON>e",
    "třina<PERSON>e",
    "štyrna<PERSON>e",
    "pjatnaće",
    "š<PERSON>snaće",
    "sydomnaće",
    "wosomnaće",
    "dźewjatnaće",
    "dwaceći",
    "třice<PERSON>i",
    "štyrceći",
    "pjećdźesat",
    "šěsćdźesat",
    "sydomdźesat",
    "wosomdźesat",
    "dźewjećdźesat",
    "sto",
    "tysac",
    "milion",
    "miliarda",
    "bilion",
    "biliarda",
    "trilion",
    "triliarda",
]

_ordinal_words = [
    "prěni",
    "prěnja",
    "prěn<PERSON>",
    "druhi",
    "druha",
    "druhe",
    "tře<PERSON><PERSON>",
    "třeća",
    "třeće",
    "štwórty",
    "štwórta",
    "štwórte",
    "pjaty",
    "pjata",
    "pjate",
    "šěsty",
    "šěsta",
    "šěste",
    "sydmy",
    "sydma",
    "sydme",
    "wosmy",
    "wosma",
    "wosme",
    "dźewjaty",
    "dźewjata",
    "dźewjate",
    "dźesaty",
    "dźesata",
    "dźesate",
    "jědnaty",
    "jědnata",
    "jědnate",
    "dwanaty",
    "dwanata",
    "dwanate",
]


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    text_lower = text.lower()
    if text_lower in _num_words:
        return True
    # Check ordinal number
    if text_lower in _ordinal_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
