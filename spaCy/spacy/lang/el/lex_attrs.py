from ...attrs import LIKE_NUM

_num_words = [
    "μηδέν",
    "ένας",
    "δυο",
    "δυό",
    "τρεις",
    "τέσσερις",
    "πέντε",
    "έξι",
    "εφτά",
    "επτ<PERSON>",
    "οκτ<PERSON>",
    "οχτ<PERSON>",
    "εννι<PERSON>",
    "εννέα",
    "δέκα",
    "έντεκα",
    "ένδεκα",
    "δώδεκα",
    "δεκατρείς",
    "δεκατέσσερις",
    "δεκαπέντε",
    "δεκαέξι",
    "δεκαεπτά",
    "δεκαοχτ<PERSON>",
    "δεκαεννέα",
    "δεκαεννεα",
    "είκοσι",
    "τριάντα",
    "σαράντα",
    "πενήντα",
    "εξήντα",
    "εβδομήντα",
    "ογδόντα",
    "ενενήντα",
    "εκατ<PERSON>",
    "διακόσιοι",
    "διακόσοι",
    "τριακόσιοι",
    "τριακόσοι",
    "τετρακόσιοι",
    "τετρακόσοι",
    "πεντακόσιοι",
    "πεντακόσοι",
    "εξακόσιοι",
    "εξακόσοι",
    "εφτακόσιοι",
    "εφτακόσοι",
    "επτακόσιοι",
    "επτακόσοι",
    "οχτακόσιοι",
    "οχτακόσοι",
    "οκτακόσιοι",
    "οκτακόσοι",
    "εννιακόσιοι",
    "χίλιοι",
    "χιλιάδα",
    "εκατομμύριο",
    "δισεκατομμύριο",
    "τρισεκατομμύριο",
    "τετράκις",
    "πεντάκις",
    "εξάκις",
    "επτάκις",
    "οκτάκις",
    "εννεάκις",
    "ένα",
    "δύο",
    "τρία",
    "τέσσερα",
    "δις",
    "χιλιάδες",
]


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.count("^") == 1:
        num, denom = text.split("^")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words or text.lower().split(" ")[0] in _num_words:
        return True
    if text in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
