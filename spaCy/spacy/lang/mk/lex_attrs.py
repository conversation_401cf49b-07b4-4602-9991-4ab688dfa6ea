from ...attrs import LIKE_NUM

_num_words = [
    "нула",
    "еден",
    "една",
    "едно",
    "два",
    "две",
    "три",
    "четири",
    "пет",
    "шест",
    "седум",
    "осум",
    "девет",
    "десет",
    "единаесет",
    "дванаесет",
    "тринаесет",
    "четиринаесет",
    "петнаесет",
    "шеснаесет",
    "седумнаесет",
    "осумнаесет",
    "деветнаесет",
    "дваесет",
    "триесет",
    "четириесет",
    "педесет",
    "шеесет",
    "седумдесет",
    "осумдесет",
    "деведесет",
    "сто",
    "двесте",
    "триста",
    "четиристотини",
    "петстотини",
    "шестотини",
    "седумстотини",
    "осумстотини",
    "деветстотини",
    "илјада",
    "илјади",
    "милион",
    "милиони",
    "милијарда",
    "милијарди",
    "билион",
    "билиони",
    "двајца",
    "тројца",
    "четворица",
    "петмина",
    "шестмина",
    "седуммина",
    "осуммина",
    "деветмина",
    "обата",
    "обајцата",
    "прв",
    "втор",
    "трет",
    "четврт",
    "седм",
    "осм",
    "двестоти",
    "два-три",
    "два-триесет",
    "два-триесетмина",
    "два-тринаесет",
    "два-тројца",
    "две-три",
    "две-тристотини",
    "пет-шеесет",
    "пет-шеесетмина",
    "пет-шеснаесетмина",
    "пет-шест",
    "пет-шестмина",
    "пет-шестотини",
    "петина",
    "осмина",
    "седум-осум",
    "седум-осумдесет",
    "седум-осуммина",
    "седум-осумнаесет",
    "седум-осумнаесетмина",
    "три-четириесет",
    "три-четиринаесет",
    "шеесет",
    "шеесетина",
    "шеесетмина",
    "шеснаесет",
    "шеснаесетмина",
    "шест-седум",
    "шест-седумдесет",
    "шест-седумнаесет",
    "шест-седумстотини",
    "шестоти",
    "шестотини",
]


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True

    text_lower = text.lower()
    if text_lower in _num_words:
        return True

    if text_lower.endswith(("а", "о", "и")):
        if text_lower[:-1] in _num_words:
            return True

    if text_lower.endswith(("ти", "та", "то", "на")):
        if text_lower[:-2] in _num_words:
            return True

    if text_lower.endswith(("ата", "иот", "ите", "ина", "чки")):
        if text_lower[:-3] in _num_words:
            return True

    if text_lower.endswith(("мина", "тина")):
        if text_lower[:-4] in _num_words:
            return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
