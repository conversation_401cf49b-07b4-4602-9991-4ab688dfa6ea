name: Explosion Bo<PERSON>

on:
  issue_comment:
    types:
      - created
      - edited

jobs:
  explosion-bot:
    if: github.repository_owner == 'explosion'
    runs-on: ubuntu-latest
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ to<PERSON>son(github) }}
        run: echo "$GITHUB_CONTEXT"
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
      - name: Install and run explosion-bot
        run: |
          pip install git+https://${{ secrets.EXPLOSIONBOT_TOKEN }}@github.com/explosion/explosion-bot
          python -m explosionbot
        env:
          INPUT_TOKEN: ${{ secrets.EXPLOSIONBOT_TOKEN }}
          INPUT_BK_TOKEN: ${{ secrets.BUILDKITE_SECRET }}
          ENABLED_COMMANDS: "test_gpu,test_slow,test_slow_gpu"
          ALLOWED_TEAMS: "spaCy"
