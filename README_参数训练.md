# AI安全防御系统参数训练与可视化

## 🎯 快速开始

### 1. 查询可训练参数
```bash
python parameter_training_visualization.py --mode query
```
输出系统中所有17个可训练超参数的详细信息。

### 2. 快速训练测试
```bash
python parameter_training_visualization.py --mode train --iterations 2 --samples 50
```
运行小规模训练，快速验证功能。

### 3. 完整训练和可视化
```bash
python parameter_training_visualization.py --mode full --iterations 5 --samples 100
```
运行完整训练并生成所有7种可视化图表。

## 📊 可训练参数概览

系统包含 **17个可训练超参数**，分为8大类：

1. **多目标优化权重** (3个): w1, w2, w3
2. **马尔可夫状态转移** (2个): gamma, beta  
3. **决策阈值** (2个): reject_threshold, clarify_threshold
4. **惩罚机制** (3个): delta1, delta2, delta3
5. **升级检测** (2个): high/moderate_escalation_threshold
6. **可信度评估** (3个): credibility相关参数
7. **轨迹分析** (2个): trajectory_penalty_weight, eta
8. **系统配置** (2个): fast_track, risk_lock_threshold

## 🎨 七大可视化功能

1. **多目标权重三元图** ⭐⭐⭐⭐⭐ - 帕累托前沿分析
2. **马尔可夫参数敏感性热力图** ⭐⭐⭐⭐ - γ和β参数优化
3. **决策阈值ROC曲线族** ⭐⭐⭐⭐⭐ - 阈值性能权衡
4. **参数收敛轨迹图** - 优化过程监控
5. **参数重要性雷达图** - 关键参数识别
6. **相关性网络图** - 参数耦合关系
7. **高维参数空间投影** - t-SNE可视化

## 📁 输出文件

训练完成后在 `training_results/` 目录生成：
- 7个可视化图表（PNG/HTML格式）
- `best_params.json` - 最佳参数配置
- `training_stats.json` - 训练统计信息

## 🔧 集成到防御系统

```python
from defensenew import AISecurityDefenseSystem
import json

# 加载最佳参数
with open('training_results/best_params.json', 'r') as f:
    best_params = json.load(f)

# 应用到防御系统
defense_system = AISecurityDefenseSystem()
defense_system.update_trainable_params(best_params)
```

## 📦 依赖包

```bash
pip install numpy matplotlib seaborn plotly pandas scikit-learn networkx
```

## 💡 使用建议

- **学术研究**: 使用多目标权重三元图展示理论优势
- **系统调优**: 使用参数敏感性分析指导优化
- **性能监控**: 使用收敛轨迹图监控训练过程
- **参数分析**: 使用相关性网络发现参数模式

---

详细说明请参考 `参数训练可视化说明.md`
