#!/usr/bin/env python3
"""
简单测试脚本，用于验证defensenew.py是否正常工作
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试AI安全防御系统基础功能")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('DEEPSEEK_API_KEY'):
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        return False
    
    try:
        # 导入系统
        from defensenew import AISecurityDefenseSystem
        print("✅ 成功导入 AISecurityDefenseSystem")
        
        # 初始化系统
        defense_system = AISecurityDefenseSystem()
        print("✅ 系统初始化成功")
        
        # 测试简单输入
        test_input = "你好"
        print(f"\n📝 测试输入: {test_input}")
        
        result = defense_system.process_input(test_input)
        
        # 检查结果
        if 'decision' in result:
            action = result['decision']['action']
            risk_score = result.get('risk_score', 0.0)
            print(f"✅ 处理成功")
            print(f"   决策: {action}")
            print(f"   风险评分: {risk_score:.3f}")
            
            # 检查系统统计
            if 'system_stats' in result:
                cache_stats = result['system_stats']['cache_performance']
                print(f"   缓存命中率: {cache_stats['hit_rate']:.2%}")
            
            return True
        else:
            print("❌ 结果格式异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n💾 测试内存使用情况")
    print("-" * 30)
    
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        print(f"当前内存使用: {memory_mb:.1f} MB")
        
        if memory_mb > 500:
            print("⚠️ 内存使用较高")
        else:
            print("✅ 内存使用正常")
            
    except ImportError:
        print("⚠️ psutil 不可用，跳过内存检查")

def main():
    """主测试函数"""
    print("🎯 AI安全防御系统简单测试")
    print("=" * 60)
    
    # 基础功能测试
    success = test_basic_functionality()
    
    # 内存使用测试
    test_memory_usage()
    
    if success:
        print("\n🎉 测试通过！系统运行正常")
    else:
        print("\n❌ 测试失败！请检查系统配置")
    
    return success

if __name__ == "__main__":
    main()
