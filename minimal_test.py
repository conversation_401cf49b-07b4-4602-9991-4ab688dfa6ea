#!/usr/bin/env python3
"""
最小化测试 - 只测试导入和初始化
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_import():
    """测试导入"""
    print("📦 测试导入...")
    try:
        from defensenew import AISecurityDefenseSystem
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_init():
    """测试初始化"""
    print("🔧 测试初始化...")
    try:
        from defensenew import AISecurityDefenseSystem
        
        # 显示内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 初始化前内存: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            pass
        
        defense_system = AISecurityDefenseSystem()
        
        # 显示初始化后内存状态
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            process_memory = psutil.Process().memory_info().rss / 1024 / 1024
            print(f"📊 初始化后内存: 系统{memory_percent:.1f}%, 进程{process_memory:.1f}MB")
        except ImportError:
            pass
        
        print("✅ 初始化成功")
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 最小化测试")
    print("=" * 30)
    
    # 检查API密钥
    if not os.getenv('DEEPSEEK_API_KEY'):
        print("❌ 请设置 DEEPSEEK_API_KEY 环境变量")
        return False
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试初始化
    if not test_init():
        return False
    
    print("\n🎉 最小化测试通过！")
    return True

if __name__ == "__main__":
    main()
