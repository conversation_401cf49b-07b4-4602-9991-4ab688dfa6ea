#!/usr/bin/env python3
"""
测试参数训练和可视化功能
简化版本，用于快速验证功能
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from parameter_training_visualization import ParameterAnalyzer, ParameterTrainer, TrainingResult, query_llm_for_parameters
    print("✅ 成功导入参数训练模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保安装了所需的依赖包：")
    print("pip install numpy matplotlib seaborn plotly pandas scikit-learn networkx")
    sys.exit(1)

def test_parameter_query():
    """测试参数查询功能"""
    print("🔍 测试参数查询功能")
    print("-" * 40)
    
    try:
        param_info = query_llm_for_parameters()
        print(f"✅ 成功查询到 {param_info['total_params']} 个可训练参数")
        return True
    except Exception as e:
        print(f"❌ 参数查询失败: {e}")
        return False

def test_parameter_generation():
    """测试参数生成功能"""
    print("\n🎲 测试参数生成功能")
    print("-" * 40)
    
    try:
        analyzer = ParameterAnalyzer()
        param_sets = analyzer.generate_random_params(n_samples=10)
        
        print(f"✅ 成功生成 {len(param_sets)} 组参数")
        
        # 验证参数约束
        for i, params in enumerate(param_sets[:3]):
            w_sum = params["w1_dangerous_weight"] + params["w2_progressive_weight"] + params["w3_clarification_weight"]
            print(f"  参数组 {i+1}: 权重和 = {w_sum:.3f}")
            
            # 检查阈值关系
            if params["reject_threshold"] > params["clarify_threshold"]:
                print(f"    ✅ 阈值关系正确: reject({params['reject_threshold']:.3f}) > clarify({params['clarify_threshold']:.3f})")
            else:
                print(f"    ❌ 阈值关系错误")
        
        return True
    except Exception as e:
        print(f"❌ 参数生成失败: {e}")
        return False

def test_performance_simulation():
    """测试性能模拟功能"""
    print("\n📊 测试性能模拟功能")
    print("-" * 40)
    
    try:
        analyzer = ParameterAnalyzer()
        param_sets = analyzer.generate_random_params(n_samples=5)
        
        results = []
        for i, params in enumerate(param_sets):
            f1, f2, f3, overall = analyzer.simulate_performance(params)
            result = TrainingResult(
                params=params,
                f1_score=f1,
                f2_score=f2,
                f3_score=f3,
                overall_score=overall,
                iteration=0
            )
            results.append(result)
            
            print(f"  参数组 {i+1}: F1={f1:.3f}, F2={f2:.3f}, F3={f3:.3f}, 总分={overall:.3f}")
        
        print(f"✅ 成功模拟 {len(results)} 组参数性能")
        return results
    except Exception as e:
        print(f"❌ 性能模拟失败: {e}")
        return []

def test_basic_visualization(results):
    """测试基础可视化功能"""
    print("\n📈 测试基础可视化功能")
    print("-" * 40)
    
    if not results:
        print("❌ 没有结果数据，跳过可视化测试")
        return False
    
    try:
        analyzer = ParameterAnalyzer()
        
        # 测试简单的散点图
        f1_scores = [r.f1_score for r in results]
        f2_scores = [r.f2_score for r in results]
        f3_scores = [r.f3_score for r in results]
        
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 3, 1)
        plt.scatter(f1_scores, f2_scores, alpha=0.7)
        plt.xlabel('F1 (错误拒绝风险)')
        plt.ylabel('F2 (错误接受风险)')
        plt.title('F1 vs F2')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 3, 2)
        plt.scatter(f1_scores, f3_scores, alpha=0.7, color='green')
        plt.xlabel('F1 (错误拒绝风险)')
        plt.ylabel('F3 (用户体验损失)')
        plt.title('F1 vs F3')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 3, 3)
        plt.scatter(f2_scores, f3_scores, alpha=0.7, color='red')
        plt.xlabel('F2 (错误接受风险)')
        plt.ylabel('F3 (用户体验损失)')
        plt.title('F2 vs F3')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('test_basic_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 基础可视化测试成功")
        print("📁 图表已保存为 test_basic_visualization.png")
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        return False

def test_parameter_importance():
    """测试参数重要性分析"""
    print("\n🎯 测试参数重要性分析")
    print("-" * 40)
    
    try:
        analyzer = ParameterAnalyzer()
        
        # 生成更多样本用于相关性分析
        param_sets = analyzer.generate_random_params(n_samples=50)
        results = []
        
        for params in param_sets:
            f1, f2, f3, overall = analyzer.simulate_performance(params)
            result = TrainingResult(
                params=params,
                f1_score=f1,
                f2_score=f2,
                f3_score=f3,
                overall_score=overall,
                iteration=0
            )
            results.append(result)
        
        # 计算简单的参数重要性
        param_names = ["w1_dangerous_weight", "w2_progressive_weight", "w3_clarification_weight", 
                      "gamma", "beta", "reject_threshold", "clarify_threshold"]
        
        print("参数重要性分析 (基于方差):")
        for param_name in param_names:
            param_values = [r.params[param_name] for r in results]
            param_variance = np.var(param_values)
            print(f"  {param_name}: 方差 = {param_variance:.4f}")
        
        print("✅ 参数重要性分析完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数重要性分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 参数训练和可视化功能测试")
    print("=" * 50)
    
    # 测试各个功能模块
    tests = [
        ("参数查询", test_parameter_query),
        ("参数生成", test_parameter_generation),
        ("性能模拟", test_performance_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        if test_name == "性能模拟":
            test_results = test_func()
            if test_results:
                results = test_results
        else:
            test_func()
    
    # 如果有结果数据，进行可视化测试
    if results:
        test_basic_visualization(results)
        test_parameter_importance()
    
    print("\n🎉 测试完成！")
    print("💡 如需完整的可视化功能，请运行:")
    print("   python parameter_training_visualization.py")

if __name__ == "__main__":
    main()
